/**
 * Wheel Size Finder - Theme Presets Panel
 * Uses CSS layers and custom properties for maintainability
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties for Theme Customization */
:root {
    --wsf-accent-neutral: #64748b;      /* slate-500 */
    --wsf-accent-neutral-hover: #475569; /* slate-600 */
    --wsf-accent-neutral-dark: #334155;  /* slate-700 */
    --wsf-accent-neutral-darker: #1e293b; /* slate-800 */
    --wsf-surface-neutral: #f1f5f9;     /* slate-100 */
    --wsf-border-neutral: #cbd5e1;      /* slate-300 */

    /* Theme Card Variables */
    --wsf-ring: 4px;                    /* Active ring width */
    --wsf-gap-swatch: 6px;              /* Gap between color swatches */
    --wsf-card-padding: 12px;           /* Consistent card padding */

    /* Admin Layout Variables */
    --wsf-admin-column-top: 0px;        /* Unified top margin for column alignment */

    /* Card Border Variables */
    --wsf-card-border-width: 2px;       /* увеличено с 1px для лучшей видимости */
    --wsf-card-border-color: var(--wsf-border-neutral, #cbd5e1); /* slate-300 */

    /* Theme Integration Variables */
    --wsf-admin-panel-bg: #ffffff;      /* Admin panel background - controlled by active theme */

    /* Interactive & Animation Variables */
    --wsf-animation-fast: 0.15s;        /* Quick interactions */
    --wsf-animation-normal: 0.3s;       /* Standard transitions */
    --wsf-animation-slow: 0.5s;         /* Smooth animations */
    --wsf-shadow-hover: 0 8px 25px rgba(0, 0, 0, 0.15);
    --wsf-shadow-active: 0 12px 35px rgba(0, 0, 0, 0.2);

    /* Preview & Modal Variables */
    --wsf-preview-size: 280px;          /* Preview modal width */
    --wsf-tooltip-bg: rgba(0, 0, 0, 0.9);
    --wsf-success-color: #10b981;       /* emerald-500 */
    --wsf-warning-color: #f59e0b;       /* amber-500 */
    --wsf-danger-color: #ef4444;        /* red-500 */
}

@layer wsf-components {
    /* Widget Title Unification - Ensure admin matches frontend */
    .wsf-widget__title {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 1.5rem !important; /* 24px */
        font-weight: 700 !important;
        line-height: 1.3 !important;
        text-align: center !important;
        margin: 0 !important;
        color: var(--wsf-text, #1f2937) !important;
    }

    /* Responsive title sizing to match frontend */
    @media (min-width: 768px) {
        .wsf-widget__title {
            font-size: 1.875rem !important; /* 30px */
        }
    }

    /* Ensure all widget elements use Inter font consistently */
    .wheel-fit-widget,
    .wheel-fit-widget *,
    .wsf-finder-widget,
    .wsf-finder-widget * {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    }

    /* Admin Grid Layout */
    .wsf-admin-grid {
        display: grid;
        grid-template-columns: 1fr 320px;
        gap: 2rem;
        align-items: stretch; /* Changed from flex-start to stretch for equal height */
    }

    /* Выравнивание основной колонки */
    .wsf-admin-grid__main {
        margin-top: var(--wsf-admin-column-top); /* синхронизация с правой колонкой */
    }

    @media (max-width: 1200px) {
        .wsf-admin-grid {
            grid-template-columns: 1fr;
        }

        .wsf-admin-grid__sidebar {
            order: -1;
        }

        /* На мобильных устройствах убираем отступы для экономии места */
        .wsf-admin-grid__main,
        .wsf-theme-panel {
            margin-top: 0;
        }
    }

    /* Theme Panel Container - now in sidebar */
    .wsf-theme-panel {
        background: #fff;                       /* фиксированный нейтральный фон */
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        /* space around the whole panel */
        padding: 24px;
        display: flex;
        flex-direction: column;
        height: fit-content;                    /* автоматическая высота */
        min-height: calc(100vh - 200px);        /* минимальная высота для выравнивания */
        margin-top: var(--wsf-admin-column-top); /* унифицированный отступ для выравнивания колонок */
    }

    /* Panel Header */
    .wsf-theme-panel__header {
        /* tighten header spacing */
        padding: 0 0 16px 0;
    }

    .wsf-theme-panel__title {
        margin: 0 0 0.5rem 0; /* даст тот самый «отступ» */
        font-size: 18px;
        font-weight: 500;
        color: #1e1e1e;
    }

    /* Panel Content */
    .wsf-theme-panel__content {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
    }

    /* Theme Cards - Modern Interactive Design */
    /* Примечание: основные стили карточек перенесены в @layer wsf-admin для избежания конфликтов */

    .wsf-theme-card:hover {
        /* Elevated hover state */
        background: #ffffff;
        border-color: var(--wsf-accent-neutral);
        transform: translateY(-2px);
        box-shadow: var(--wsf-shadow-hover);
    }

    .wsf-theme-card--active {
        /* Active state with ring */
        background: #ffffff;
        border-color: var(--wsf-accent-neutral-dark);
        box-shadow: 0 0 0 var(--wsf-ring) var(--wsf-accent-neutral-dark),
                    var(--wsf-shadow-active);
        transform: translateY(-1px);
    }

    .wsf-theme-card--active:hover {
        /* Active + hover combination */
        transform: translateY(-3px);
        box-shadow: 0 0 0 var(--wsf-ring) var(--wsf-accent-neutral-dark),
                    var(--wsf-shadow-active);
    }

    /* Selection animation */
    .wsf-theme-card--selecting {
        animation: wsf-card-select var(--wsf-animation-normal) ease-out;
    }

    @keyframes wsf-card-select {
        0% { transform: scale(1); }
        50% { transform: scale(1.02); }
        100% { transform: scale(1); }
    }

    .wsf-theme-card__header {
        position: relative;              /* создаём якорь */
        margin-bottom: 12px;
    }

    .wsf-theme-card__name {
        font-size: 14px;
        font-weight: 600;
        color: inherit; /* Наследует цвет от родительского элемента */
        margin: 0;
        padding-right: 48px;             /* ширина блока иконок */
    }

    .wsf-theme-card__actions {
        position: absolute;
        top: calc(var(--wsf-card-padding) + var(--wsf-ring) + 4px);  /* Clear ring + 4px lower */
        right: calc(var(--wsf-card-padding) - 4px);  /* 4px more to the right */
        display: inline-flex;
        gap: 4px;                        /* Tighter spacing */
        opacity: 0;
        transition: opacity 0.15s ease;
        z-index: 1;                      /* Below badge */
    }

    /* AC-3: Responsive positioning for narrow widths */
    @media (max-width: 768px) {
        .wsf-theme-card__actions {
            top: calc(var(--wsf-card-padding) + 24px + 4px);  /* Below badge + 4px lower */
            right: calc(var(--wsf-card-padding) - 4px);  /* 4px more to the right */
        }
    }

    /* Modern Action Dropdown */
    .wsf-theme-card__dropdown {
        position: relative;
        display: inline-block;
    }

    .wsf-theme-card__dropdown-toggle {
        width: 28px;
        height: 28px;
        border: none;
        background: rgba(0, 0, 0, 0.1); /* Темный полупрозрачный фон */
        border-radius: 6px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all var(--wsf-animation-fast) ease;
        color: #374151; /* Темно-серый цвет */
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* Легкая тень */
    }

    .wsf-theme-card__dropdown-toggle:hover {
        background: rgba(0, 0, 0, 0.2); /* Более темный при наведении */
        transform: scale(1.05);
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15); /* Усиленная тень */
    }

    .wsf-theme-card__dropdown-menu {
        position: absolute;
        top: 100%;
        right: 0;
        background: #ffffff;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        min-width: 140px;
        opacity: 0;
        visibility: hidden;
        transform: translateY(-10px);
        transition: all var(--wsf-animation-fast) ease;
        z-index: 10;
    }

    .wsf-theme-card__dropdown--open .wsf-theme-card__dropdown-menu {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .wsf-theme-card__dropdown-item {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        color: #374151;
        text-decoration: none;
        font-size: 13px;
        transition: background-color var(--wsf-animation-fast) ease;
        border: none;
        background: none;
        width: 100%;
        text-align: left;
        cursor: pointer;
    }

    .wsf-theme-card__dropdown-item:hover {
        background: #f3f4f6;
    }

    .wsf-theme-card__dropdown-item--danger:hover {
        background: #fef2f2;
        color: var(--wsf-danger-color);
    }

    .wsf-theme-card__dropdown-item:first-child {
        border-radius: 8px 8px 0 0;
    }

    .wsf-theme-card__dropdown-item:last-child {
        border-radius: 0 0 8px 8px;
    }

    /* Theme Preview Modal */
    .wsf-theme-preview {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: var(--wsf-preview-size);
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
        z-index: 100001;
        opacity: 0;
        visibility: hidden;
        transition: all var(--wsf-animation-normal) ease;
    }

    .wsf-theme-preview--active {
        opacity: 1;
        visibility: visible;
    }

    .wsf-theme-preview__header {
        padding: 16px 20px;
        border-bottom: 1px solid #e2e8f0;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .wsf-theme-preview__title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
    }

    .wsf-theme-preview__close {
        width: 32px;
        height: 32px;
        border: none;
        background: #f3f4f6;
        border-radius: 6px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background-color var(--wsf-animation-fast) ease;
    }

    .wsf-theme-preview__close:hover {
        background: #e5e7eb;
    }

    .wsf-theme-preview__content {
        padding: 20px;
    }

    .wsf-theme-preview__widget {
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 16px;
        background: #f9fafb;
        font-size: 12px;
        color: #6b7280;
        text-align: center;
        min-height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .wsf-theme-preview__actions {
        padding: 16px 20px;
        border-top: 1px solid #e2e8f0;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
    }

    .wsf-theme-preview__button {
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all var(--wsf-animation-fast) ease;
        border: 1px solid transparent;
    }

    .wsf-theme-preview__button--primary {
        background: var(--wsf-accent-neutral-dark);
        color: #ffffff;
        border-color: var(--wsf-accent-neutral-dark);
    }

    .wsf-theme-preview__button--primary:hover {
        background: var(--wsf-accent-neutral-darker);
    }

    .wsf-theme-preview__button--secondary {
        background: #ffffff;
        color: var(--wsf-accent-neutral-dark);
        border-color: #d1d5db;
    }

    .wsf-theme-preview__button--secondary:hover {
        background: #f9fafb;
    }

    /* Tooltips */
    .wsf-tooltip {
        position: relative;
        display: inline-block;
    }

    .wsf-tooltip__content {
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: var(--wsf-tooltip-bg);
        color: #ffffff;
        padding: 6px 10px;
        border-radius: 6px;
        font-size: 12px;
        white-space: nowrap;
        max-width: 200px;                  /* ограничиваем ширину */
        text-overflow: ellipsis;           /* обрезаем длинные названия */
        overflow: hidden;                  /* скрываем переполнение */
        opacity: 0;
        visibility: hidden;
        transition: all var(--wsf-animation-fast) ease;
        z-index: 1000;
        margin-bottom: 5px;
    }

    .wsf-tooltip__content::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: var(--wsf-tooltip-bg);
    }

    .wsf-tooltip:hover .wsf-tooltip__content {
        opacity: 1;
        visibility: visible;
    }

    /* Enhanced Add New Theme Button */
    .wsf-theme-add {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        width: 100%;
        padding: 16px;
        border: 2px dashed #cbd5e1;
        border-radius: 12px;
        background: #ffffff;
        color: var(--wsf-accent-neutral);
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all var(--wsf-animation-normal) ease;
        margin-top: 16px;
        position: relative;
        overflow: hidden;
    }

    .wsf-theme-add::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(100, 116, 139, 0.1), transparent);
        transition: left var(--wsf-animation-slow) ease;
    }

    .wsf-theme-add:hover {
        border-color: var(--wsf-accent-neutral);
        color: var(--wsf-accent-neutral-dark);
        background: rgba(100, 116, 139, 0.02);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .wsf-theme-add:hover::before {
        left: 100%;
    }

    .wsf-theme-add__icon {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: var(--wsf-accent-neutral);
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        transition: all var(--wsf-animation-fast) ease;
    }

    .wsf-theme-add:hover .wsf-theme-add__icon {
        background: var(--wsf-accent-neutral-dark);
        transform: rotate(90deg);
    }

    /* AJAX Loading & Notifications */
    .wsf-loading {
        position: relative;
        pointer-events: none;
        opacity: 0.6;
    }

    .wsf-loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid transparent;
        border-top-color: var(--wsf-accent-neutral);
        border-radius: 50%;
        animation: wsf-spin 1s linear infinite;
        z-index: 1;
    }

    @keyframes wsf-spin {
        to { transform: rotate(360deg); }
    }

    .wsf-notification {
        position: fixed;
        top: 60px; /* Опущено ниже WordPress админ-бара */
        right: 20px;
        padding: 12px 16px;
        border-radius: 8px;
        color: #ffffff;
        font-size: 14px;
        font-weight: 500;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        z-index: 100000;
        transform: translateX(100%);
        transition: transform var(--wsf-animation-normal) ease;
    }

    .wsf-notification--show {
        transform: translateX(0);
    }

    .wsf-notification--success {
        background: var(--wsf-success-color);
    }

    .wsf-notification--warning {
        background: var(--wsf-warning-color);
    }

    .wsf-notification--error {
        background: var(--wsf-danger-color);
    }

    /* Modal Overlay */
    .wsf-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999999; /* Увеличиваем z-index для модального окна */
        opacity: 0;
        visibility: hidden;
        transition: all var(--wsf-animation-normal) ease;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .wsf-overlay--active {
        opacity: 1;
        visibility: visible;
    }

    /* Modal Dialog */
    .wsf-modal {
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        max-width: 400px;
        width: 90%;
        max-height: 90vh;
        overflow: hidden;
        transform: scale(0.95);
        transition: transform var(--wsf-animation-normal) ease;
    }

    .wsf-overlay--active .wsf-modal {
        transform: scale(1);
    }

    .wsf-modal__header {
        padding: 20px 24px 16px;
        border-bottom: 1px solid #e5e7eb;
    }

    .wsf-modal__title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #111827;
    }

    .wsf-modal__body {
        padding: 20px 24px;
    }

    .wsf-modal__message {
        margin: 0 0 16px;
        color: #374151;
        line-height: 1.5;
    }

    .wsf-modal__input {
        width: 100%;
        padding: 12px 16px;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.2s ease, box-shadow 0.2s ease;
        box-sizing: border-box;
    }

    .wsf-modal__input:focus {
        outline: none;
        border-color: var(--wsf-primary);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .wsf-modal__footer {
        padding: 16px 24px 20px;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        border-top: 1px solid #e5e7eb;
    }

    .wsf-modal__button {
        padding: 10px 20px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 1px solid transparent;
        min-width: 80px;
        position: relative; /* Обеспечиваем правильное позиционирование */
        z-index: 1; /* Убеждаемся что кнопки кликабельны */
        pointer-events: auto; /* Принудительно включаем события мыши */
    }

    .wsf-modal__button--primary {
        background: var(--wsf-primary);
        color: #ffffff;
        border-color: var(--wsf-primary);
    }

    .wsf-modal__button--primary:hover {
        background: var(--wsf-hover);
        border-color: var(--wsf-hover);
    }

    .wsf-modal__button--secondary {
        background: #ffffff;
        color: #374151;
        border-color: #d1d5db;
    }

    .wsf-modal__button--secondary:hover {
        background: #f9fafb;
        border-color: #9ca3af;
    }

    .wsf-modal__button--danger {
        background: var(--wsf-danger-color);
        color: #ffffff;
        border-color: var(--wsf-danger-color);
    }

    .wsf-modal__button--danger:hover {
        background: #dc2626;
        border-color: #dc2626;
    }

    /* Active State Indicator */
    .wsf-theme-card__status {
        position: absolute;
        top: 8px;
        left: 8px;
        width: 24px;
        height: 24px;
        background: var(--wsf-accent-neutral-dark, #334155); /* единый цвет для чистоты */
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #ffffff;
        font-size: 12px;
        font-weight: 600;
        opacity: 0;
        transform: scale(0.8);
        transition: all var(--wsf-animation-normal) ease;
    }

    .wsf-theme-card--active .wsf-theme-card__status {
        opacity: 1;
        transform: scale(1);
    }

    /* Drag & Drop Support */
    .wsf-theme-card--dragging {
        opacity: 0.5;
        transform: rotate(5deg);
        z-index: 1000;
    }

    .wsf-theme-card--drop-target {
        border-color: var(--wsf-accent-neutral);
        background: rgba(100, 116, 139, 0.05);
    }

    .wsf-theme-card--drop-target::before {
        content: '';
        position: absolute;
        top: -2px;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--wsf-accent-neutral);
        border-radius: 2px;
    }

    .wsf-theme-card:hover .wsf-theme-card__actions {
        opacity: 1;
    }

    /* Action Buttons */
    .wsf-theme-card__action {
        width: 24px;
        height: 24px;
        border: none;
        background: transparent;
        cursor: pointer;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .wsf-theme-card__action:hover {
        background: #f0f0f0;
        color: #1e1e1e;
    }

    .wsf-theme-card__action--delete:hover {
        background: #fee;
        color: #d63638;
    }

    /* Color Swatches - AC-4: Consistent spacing */
    .wsf-theme-card__colors {
        display: flex;
        gap: var(--wsf-gap-swatch);
        flex-wrap: nowrap;                 /* AC-6: гарантируем, что три цвета не ломаются строкой */
        overflow: auto;                    /* на случай очень узких экранов */
    }

    .wsf-theme-card__swatch {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        border: 1px solid rgba(0, 0, 0, 0.08);  /* Subtle border for better definition */
        position: relative;                /* AC-6: якорь для псевдо‑элемента */
        overflow: hidden;
        flex-shrink: 0;  /* Prevent shrinking with many swatches */
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05); /* Subtle inner shadow for depth */
    }

    /* Enhanced border for very light colors */
    .wsf-theme-card__swatch[style*="#F"]:not([style*="#F0"]):not([style*="#F1"]):not([style*="#F2"]):not([style*="#F3"]):not([style*="#F4"]):not([style*="#F5"]):not([style*="#F6"]):not([style*="#F7"]),
    .wsf-theme-card__swatch[style*="#f"]:not([style*="#f0"]):not([style*="#f1"]):not([style*="#f2"]):not([style*="#f3"]):not([style*="#f4"]):not([style*="#f5"]):not([style*="#f6"]):not([style*="#f7"]) {
        border-color: rgba(0, 0, 0, 0.15); /* Stronger border for very light colors */
    }

    /* AC-6: Универсальная рамка поверх заливки через псевдоэлемент */
    .wsf-theme-card__swatch::before {
        content: '';
        position: absolute;
        inset: 0;                          /* растягиваем во весь квадрат */
        border-radius: inherit;            /* повторяем скругление */
        pointer-events: none;              /* клики проходят сквозь */
        box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.06) inset;
        /* ↑ более мягкая рамка для светлых тем */
    }

    .wsf-theme-card__swatch::after {
        content: attr(data-label);        /* короткая аббревиатура (Bg, Txt, Prm) */
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.8);   /* чуть контрастнее для читаемости */
        color: #fff;
        font-size: 9px;
        font-weight: 500;                 /* чуть жирнее для лучшей читаемости */
        text-align: center;
        padding: 2px;
        opacity: 0;
        transition: opacity 0.2s ease;
        white-space: nowrap;              /* предотвращаем перенос */
    }

    .wsf-theme-card__swatch:hover::after {
        opacity: 1;
    }

    /* Special styling for dark theme swatches */
    .wsf-theme-card[data-theme-id="dark"] .wsf-theme-card__swatch {
        border-color: rgba(255, 255, 255, 0.3); /* More visible border for dark colors */
        box-shadow: inset 0 1px 2px rgba(255, 255, 255, 0.1),
                    0 2px 4px rgba(0, 0, 0, 0.3); /* Enhanced shadow for dark theme */
    }

    /* Improved contrast for theme card backgrounds */
    .wsf-theme-card[data-theme-id="dark"] {
        background: linear-gradient(135deg, #64748b 0%, #94a3b8 100%); /* Much lighter dark gradient */
        color: #ffffff; /* White text for better contrast */
    }

    .wsf-theme-card[data-theme-id="light"] {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%); /* Light gradient */
        color: #1f2937; /* Dark text for light background */
    }

    .wsf-theme-card[data-theme-id="blue"] {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%); /* Blue gradient */
        color: #0c4a6e; /* Dark blue text */
    }

    .wsf-theme-card[data-theme-id="green"] {
        background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%); /* Green gradient */
        color: #14532d; /* Dark green text */
    }

    .wsf-theme-card[data-theme-id="purple"] {
        background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%); /* Purple gradient */
        color: #581c87; /* Dark purple text */
    }

    /* Special styling for dark theme buttons */
    .wsf-theme-card[data-theme-id="dark"] .wsf-theme-card__dropdown-toggle {
        background: rgba(0, 0, 0, 0.8) !important; /* Черный фон для темной темы */
        color: #ffffff !important; /* Белые иконки на черном фоне */
        border: 1px solid rgba(255, 255, 255, 0.2); /* Светлая рамка для видимости */
    }

    .wsf-theme-card[data-theme-id="dark"] .wsf-theme-card__dropdown-toggle:hover {
        background: rgba(0, 0, 0, 1) !important; /* Полностью черный при наведении */
        color: #ffffff !important; /* Белые иконки */
        border-color: rgba(255, 255, 255, 0.4); /* Более яркая рамка */
    }

    /* Add Theme Button */
    .wsf-theme-add {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        width: 100%;
        padding: 12px;
        border: 2px dashed #ccc;
        border-radius: 8px;
        background: transparent;
        color: #666;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
        margin-top: 16px;
    }

    .wsf-theme-add:hover,
    .wsf-theme-add:focus-visible {
        border-style: dashed;
        border-color: var(--wsf-accent-neutral);
        box-shadow: 0 0 0 2px var(--wsf-accent-neutral);
        background: rgba(100,116,139,.05);
        color: var(--wsf-accent-neutral-dark);
    }

    /* Theme Editor Modal */
    .wsf-theme-editor {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 680px;
        max-width: 95vw;
        max-height: 90vh;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
        z-index: 100000;
        display: none;
        overflow: hidden;
    }

    .wsf-theme-editor--active {
        display: block;
    }

    .wsf-theme-editor__header {
        padding: 20px 24px;
        border-bottom: 1px solid #e0e0e0;
    }

    .wsf-theme-editor__title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #1e1e1e;
    }

    .wsf-theme-editor__content {
        padding: 24px;
        max-height: calc(90vh - 140px);
        overflow-y: auto;
    }

    .wsf-theme-editor__field {
        margin-bottom: 20px;
    }

    .wsf-theme-editor__label {
        display: block;
        margin-bottom: 8px;
        font-size: 14px;
        font-weight: 500;
        color: #1e1e1e;
    }

    .wsf-theme-editor__input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    .wsf-theme-editor__color-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
    }

    .wsf-theme-editor__color-field {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .wsf-theme-editor__color-input {
        width: 48px;
        height: 36px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
    }

    .wsf-theme-editor__color-label-wrapper {
        flex: 1;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .wsf-theme-editor__color-label {
        font-size: 13px;
        color: #666;
        margin: 0;
    }

    .wsf-theme-editor__help-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 18px;
        height: 18px;
        color: #999;
        cursor: help;
        transition: color 0.2s ease;
    }

    .wsf-theme-editor__help-icon:hover {
        color: #666;
    }

    /* Contrast indicator styles */
    .wsf-contrast-indicator {
        display: inline-flex;
        align-items: center;
        gap: 4px;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: 600;
        text-transform: uppercase;
        margin-left: 8px;
    }

    .wsf-contrast-indicator--good {
        background: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .wsf-contrast-indicator--poor {
        background: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .wsf-contrast-indicator__icon {
        font-size: 8px;
    }

    .wsf-contrast-indicator__text {
        font-size: 9px;
        letter-spacing: 0.5px;
    }

    .wsf-theme-editor__footer {
        padding: 16px 24px;
        border-top: 1px solid #e0e0e0;
        display: flex;
        justify-content: flex-end;
        gap: 12px;
    }

    .wsf-theme-editor__button {
        padding: 10px 20px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 1px solid transparent;
        min-width: 80px;
    }

    .wsf-theme-editor__button--primary {
        background: var(--wsf-accent-neutral);
        color: #ffffff;
        border-color: var(--wsf-accent-neutral);
    }

    .wsf-theme-editor__button--primary:hover {
        background: var(--wsf-accent-neutral-hover);
        border-color: var(--wsf-accent-neutral-hover);
    }

    .wsf-theme-editor__button--cancel {
        background: #ffffff;
        color: #374151;
        border-color: #d1d5db;
    }

    .wsf-theme-editor__button--cancel:hover {
        background: #f9fafb;
        border-color: #9ca3af;
    }

    /* Color Cheat Sheet Styles - Adaptive Container */
    .wsf-cheat-sheet {
        margin-top: 20px;
        width: auto;
        max-width: 100%;
        box-sizing: border-box;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        background: #f9fafb;
        transition: all 0.3s ease;
        overflow: hidden;
    }



    .wsf-cheat-sheet__header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        border-bottom: 1px solid #e5e7eb;
        background: #ffffff;
        border-radius: 8px 8px 0 0;
    }

    .wsf-cheat-sheet__title {
        margin: 0;
        font-size: 18px; /* text-lg */
        font-weight: 600; /* font-semibold */
        color: #111827; /* text-gray-900 */
        line-height: 1.5;
    }

    .wsf-cheat-sheet__toggle {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 16px;
        border: 1px solid #d1d5db;
        border-radius: 8px;
        background: #ffffff;
        color: #374151;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }

    .wsf-cheat-sheet__toggle:hover {
        background: #f9fafb;
        border-color: #9ca3af;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .wsf-cheat-sheet__toggle-icon {
        transition: transform 0.2s ease;
        flex-shrink: 0;
    }

    .wsf-cheat-sheet__content {
        padding: 0;
        max-height: 400px; /* Ограничиваем высоту, чтобы модалка не раздувалась */
        overflow: auto; /* Оба направления прокрутки при необходимости */
    }

    .wsf-cheat-sheet__table-wrapper {
        padding: 16px; /* Отступы */
        border-radius: 0 0 12px 12px; /* rounded-b-xl */
        overflow: auto; /* Скролл при переполнении */
        -webkit-overflow-scrolling: touch; /* Плавная прокрутка на iOS */
    }

    .wsf-cheat-sheet__table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        font-size: 14px;
        margin: 0;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    /* Allow code snippets to wrap to avoid horizontal overflow */
    .wsf-cheat-sheet__code {
        word-wrap: break-word;
        word-break: break-all;
    }

    .wsf-cheat-sheet__table th {
        background: #f9fafb;
        padding: 16px;
        text-align: left;
        font-weight: 600;
        color: #111827;
        border-bottom: 1px solid #e5e7eb;
        font-size: 14px;
        letter-spacing: 0.025em;
        text-transform: uppercase;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .wsf-cheat-sheet__table th:first-child {
        border-top-left-radius: 8px;
    }

    .wsf-cheat-sheet__table th:last-child {
        border-top-right-radius: 8px;
    }

    .wsf-cheat-sheet__table td {
        padding: 16px;
        border-bottom: 1px solid #f3f4f6;
        vertical-align: top;
        text-align: left;
        background: #ffffff;
    }

    .wsf-cheat-sheet__row:hover td {
        background: #f9fafb;
    }

    .wsf-cheat-sheet__row:last-child td {
        border-bottom: none;
    }

    .wsf-cheat-sheet__row:last-child td:first-child {
        border-bottom-left-radius: 8px;
    }

    .wsf-cheat-sheet__row:last-child td:last-child {
        border-bottom-right-radius: 8px;
    }

    .wsf-cheat-sheet__token {
        font-weight: 600;
        color: #111827;
        min-width: 120px;
        font-size: 15px;
    }

    .wsf-cheat-sheet__description {
        color: #6b7280;
        max-width: 200px;
        line-height: 1.5;
    }

    .wsf-cheat-sheet__vars {
        position: relative;
        min-width: 180px;
    }

    .wsf-cheat-sheet__var-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        gap: 8px;
    }

    .wsf-cheat-sheet__var-item:last-child {
        margin-bottom: 0;
    }

    .wsf-cheat-sheet__code {
        background: #f3f4f6;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        padding: 6px 12px;
        font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
        font-size: 13px;
        color: #dc2626;
        display: inline-block;
        flex: 1;
        font-weight: 500;
        letter-spacing: -0.025em;
    }

    .wsf-cheat-sheet__copy {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 28px;
        height: 28px;
        min-width: 28px;
        border: 1px solid #d1d5db;
        border-radius: 6px;
        background: #ffffff;
        color: #6b7280;
        cursor: pointer;
        transition: all 0.2s ease;
        vertical-align: middle;
        padding: 0;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }

    .wsf-cheat-sheet__copy:hover {
        background: #f9fafb;
        border-color: #9ca3af;
        color: #374151;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .wsf-cheat-sheet__copy--copied {
        background: #dcfce7;
        border-color: #bbf7d0;
        color: #166534;
    }

    .wsf-cheat-sheet__example {
        color: #6b7280;
        font-style: italic;
        max-width: 150px;
        line-height: 1.5;
        font-size: 13px;
    }

    .wsf-cheat-sheet__empty {
        text-align: center;
        color: #9ca3af;
        font-style: italic;
        padding: 32px;
        font-size: 14px;
    }

    /* Responsive Design for Modal Context */
    @media (max-width: 768px) {
        .wsf-cheat-sheet {
            margin: 8px 0; /* Убираем боковые отступы в модалке */
            border-radius: 8px;
        }

        .wsf-cheat-sheet__header {
            padding: 12px 16px; /* Уменьшаем отступы */
            flex-direction: column;
            gap: 8px;
            align-items: stretch;
        }

        .wsf-cheat-sheet__title {
            font-size: 16px;
            text-align: center;
        }

        .wsf-cheat-sheet__toggle {
            justify-content: center;
            padding: 6px 12px;
            font-size: 13px;
        }

        .wsf-cheat-sheet__content {
            max-height: 300px; /* Меньше высота на мобильных */
        }

        .wsf-cheat-sheet__table-wrapper {
            padding: 12px; /* Меньше отступы */
        }

        .wsf-cheat-sheet__table {
            font-size: 12px;
            min-width: 500px; /* Уменьшаем минимальную ширину */
        }

        .wsf-cheat-sheet__table th,
        .wsf-cheat-sheet__table td {
            padding: 8px 6px; /* Компактные отступы */
        }

        .wsf-cheat-sheet__code {
            font-size: 11px;
            padding: 3px 6px;
        }

        .wsf-cheat-sheet__copy {
            width: 22px;
            height: 22px;
            min-width: 22px;
        }

        .wsf-cheat-sheet__description,
        .wsf-cheat-sheet__example {
            font-size: 11px;
        }

        .wsf-cheat-sheet__vars {
            min-width: 140px; /* Уменьшаем минимальную ширину */
        }
    }

    /* Extra small screens (modal context) */
    @media (max-width: 480px) {
        .wsf-cheat-sheet {
            margin: 4px 0;
            border-radius: 6px;
        }

        .wsf-cheat-sheet__header {
            padding: 8px 12px;
        }

        .wsf-cheat-sheet__title {
            font-size: 14px;
        }

        .wsf-cheat-sheet__content {
            max-height: 250px; /* Еще меньше на очень маленьких экранах */
        }

        .wsf-cheat-sheet__table-wrapper {
            padding: 8px;
        }

        .wsf-cheat-sheet__table {
            font-size: 11px;
            min-width: 400px; /* Минимум для читаемости */
        }

        .wsf-cheat-sheet__table th,
        .wsf-cheat-sheet__table td {
            padding: 6px 4px;
        }

        .wsf-cheat-sheet__var-item {
            margin-bottom: 4px;
            gap: 4px;
        }

        .wsf-cheat-sheet__code {
            font-size: 10px;
            padding: 2px 4px;
        }

        .wsf-cheat-sheet__copy {
            width: 20px;
            height: 20px;
            min-width: 20px;
        }
    }

    /* Grid layout for better token display */
    .wsf-cheat-sheet__grid {
        display: grid;
        grid-template-columns: 1fr 2fr 2fr 1.5fr;
        gap: 16px;
        align-items: start;
    }

    @media (max-width: 768px) {
        .wsf-cheat-sheet__grid {
            grid-template-columns: 1fr;
            gap: 12px;
        }
    }

    /* ===== CRITICAL FIX: Color Tokens Reference Modal Layout ================ */
    /* High specificity patch to override all previous rules and fix layout issues */

    @layer utilities {
        /* ===== 1. Single scroll area - modal content only ==================== */
        .wsf-theme-editor {
            display: flex !important;
            flex-direction: column !important;
            max-height: 90vh !important;
            overflow: hidden !important;          /* contain scrolling within */
        }

        .wsf-theme-editor__content {
            flex: 1 1 auto !important;
            min-height: 0 !important;             /* critical for flex + scroll behavior */
            overflow-y: auto !important;
            max-height: none !important;
            padding: 20px !important;
        }

        /* ===== 2. Remove independent scrolling from cheat sheet ================ */
        .wsf-cheat-sheet,
        .wsf-cheat-sheet.wsf-cheat-sheet {
            max-height: none !important;
            overflow: visible !important;
            width: 100% !important;
            max-width: 100% !important;
            margin: 16px 0 !important;
            border-left: 0 !important;           /* remove unnecessary left border */
        }

        .wsf-cheat-sheet__content,
        .wsf-cheat-sheet__content.wsf-cheat-sheet__content {
            max-height: none !important;
            overflow: visible !important;
            overflow-x: visible !important;
        }

        .wsf-cheat-sheet__table-wrapper,
        .wsf-cheat-sheet__table-wrapper.wsf-cheat-sheet__table-wrapper {
            max-height: none !important;
            overflow: visible !important;
            overflow-x: auto !important;  /* horizontal scroll only if needed */
            padding: 16px !important;
        }

        /* ===== 3. Table improvements ================================= */
        .wsf-cheat-sheet__table,
        .wsf-cheat-sheet__table.wsf-cheat-sheet__table {
            border-left: 0 !important;           /* remove unnecessary left border */
            min-width: 580px !important;         /* ensure readability */
        }

        /* ===== 4. Responsive adjustments ============================= */
        @media (max-width: 768px) {
            .wsf-theme-editor {
                max-height: 95vh !important;
            }

            .wsf-theme-editor__content {
                padding: 12px !important;
            }

            .wsf-cheat-sheet {
                margin: 8px 0 !important;
            }

            .wsf-cheat-sheet__table-wrapper {
                padding: 12px !important;
            }

            .wsf-cheat-sheet__table {
                min-width: 480px !important;
            }
        }

        @media (max-width: 480px) {
            .wsf-theme-editor {
                max-height: 98vh !important;
            }

            .wsf-theme-editor__content {
                padding: 8px !important;
            }

            .wsf-cheat-sheet {
                margin: 4px 0 !important;
            }

            .wsf-cheat-sheet__table-wrapper {
                padding: 8px !important;
            }

            .wsf-cheat-sheet__table {
                min-width: 400px !important;
            }
        }
    }

    /* Mobile responsive styles */
    @media (max-width: 768px) {
        .wsf-theme-editor {
            width: 95vw;
            max-height: 95vh;
        }

        .wsf-cheat-sheet__table th:nth-child(2),
        .wsf-cheat-sheet__table td:nth-child(2) {
            display: none;
        }

        .wsf-cheat-sheet__table th:nth-child(4),
        .wsf-cheat-sheet__table td:nth-child(4) {
            display: none;
        }

        .wsf-cheat-sheet__token {
            position: relative;
        }

        .wsf-cheat-sheet__token::after {
            content: "ℹ️";
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            cursor: help;
            font-size: 12px;
        }

        .wsf-cheat-sheet__vars {
            min-width: auto;
        }

        .wsf-cheat-sheet__code {
            font-size: 10px;
            padding: 1px 4px;
        }

        .wsf-cheat-sheet__copy {
            width: 18px;
            height: 18px;
            min-width: 18px;
        }

        .wsf-cheat-sheet__table-wrapper {
            max-height: 250px;
        }
    }

    @media (max-width: 480px) {
        .wsf-cheat-sheet__table {
            font-size: 11px;
        }

        .wsf-cheat-sheet__table th,
        .wsf-cheat-sheet__table td {
            padding: 8px 6px;
        }

        .wsf-cheat-sheet__var-item {
            margin-bottom: 4px;
        }
    }

    /* Modal Overlay */
    .wsf-theme-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 99999;
        display: none;
    }

    .wsf-theme-overlay--active {
        display: block;
    }

    /* Responsive Design */
    @media (max-width: 1100px) {
        .wsf-theme-panel {
            position: relative;
            top: auto;
            right: auto;
            width: 100%;
            height: auto;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-top: 20px;
        }

        body.rtl .wsf-theme-panel {
            left: auto;
            border: 1px solid #e0e0e0;
        }
    }

    /* Loading State */
    .wsf-theme-panel--loading .wsf-theme-panel__content {
        opacity: 0.5;
        pointer-events: none;
    }

    .wsf-theme-panel__loader {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 32px;
        height: 32px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid var(--wsf-accent-neutral);
        border-radius: 50%;
        animation: wsf-spin 1s linear infinite;
        display: none;
    }

    .wsf-theme-panel--loading .wsf-theme-panel__loader {
        display: block;
    }

    @keyframes wsf-spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* Active Badge - AC-3: Fixed positioning */
    .wsf-theme-card__badge {
        position: absolute;
        top: var(--wsf-card-padding);
        right: var(--wsf-card-padding);
        background: #10b981; /* Зеленый фон для активного состояния */
        color: #ffffff; /* Белый текст */
        font-size: 11px;
        font-weight: 600;
        padding: 2px 8px;
        border-radius: 12px;
        text-transform: uppercase;
        z-index: 2;  /* Above actions */
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2); /* Тень для лучшей видимости */
    }

    body.rtl .wsf-theme-card__badge {
        right: auto;
        left: var(--wsf-card-padding);
    }

    /* Feature Flag Notice */
    .wsf-theme-panel__notice {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
        padding: 12px 16px;
        margin: 0 20px 20px;
        border-radius: 4px;
        font-size: 13px;
    }

    .wsf-theme-panel__notice--info {
        background: #d1ecf1;
        border-color: #bee5eb;
        color: #0c5460;
    }
}



/* ------------------------------------------------------------------
 *  Wheel‑Size Finder – Admin Theme Panel
 * ----------------------------------------------------------------*/
@layer wsf-admin {
  /* Panel Content */
  .wsf-theme-panel__content  {
    /* improved breathing room: 24px top/bottom, 16px left/right */
    @apply wsf-py-6 wsf-px-4 wsf-flex wsf-flex-col wsf-gap-3 wsf-overflow-y-auto;
  }

  /* Theme Cards */
  .wsf-theme-card {
    /* Tailwind-утилиты оставляем для цвета и других свойств,
       а ширину и стиль границы задаём через переменные */
    @apply wsf-bg-white
           wsf-border-slate-300  /* цвет из палитры */
           wsf-rounded-lg
           wsf-shadow-sm
           wsf-transition
           wsf-relative wsf-p-3
           wsf-flex wsf-flex-col wsf-gap-2
           wsf-cursor-pointer;

    /* Граница через переменные для гибкости */
    border: var(--wsf-card-border-width) solid var(--wsf-card-border-color);

    /* Дополнительные стили, которые нельзя выразить через @apply */
    gap: var(--wsf-gap-swatch);
    padding: var(--wsf-card-padding);
    margin-bottom: 20px;
    transform: translateY(0);
    transition: all var(--wsf-animation-normal) cubic-bezier(0.4, 0, 0.2, 1);
    /* Базовая тень */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  }

  .wsf-theme-card:hover {
    background: rgba(100,116,139,.06);
    box-shadow: 0 6px 10px -2px rgba(0,0,0,.08),
                0  3px  6px -2px rgba(0,0,0,.04);
  }

  /* активное «кольцо» остаётся поверх новой рамки */
  .wsf-theme-card--active {
    box-shadow: 0 0 0 var(--wsf-ring) var(--wsf-accent-neutral-dark, #334155);
  }

  /* Dark mode support removed for consistent light interface */

  .wsf-theme-card__badge {
      /* зеленый бейдж для активного состояния */
    background: #10b981;
    color: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  /* контейнер цветов */
  .wsf-theme-card__colors { @apply wsf-flex wsf-gap-2 wsf-flex-nowrap wsf-overflow-auto; }

  /* свотч */
  .wsf-theme-card__swatch { @apply wsf-w-6 wsf-h-6 wsf-rounded-sm; }

  /* «Add New Theme» */
  .wsf-theme-panel__add   { @apply wsf-inline-flex wsf-items-center wsf-gap-1 wsf-text-primary hover:wsf-text-primary-dark wsf-text-sm wsf-font-medium wsf-mt-2; }

  /* разделитель между карточками */
  .wsf-theme-panel__content > * + * { @apply wsf-pt-3 wsf-border-t wsf-border-slate-200; }
} 