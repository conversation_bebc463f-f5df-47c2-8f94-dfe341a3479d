<?php
declare(strict_types=1);

namespace MyTyreFinder\Admin;

use My<PERSON>yre<PERSON>inder\Translations\TranslationManager;
use MyTyreFinder\Includes\ColorTokens;
use MyTyreFinder\Includes\ThemeManager;

/**
 * Экран «Wheel-Size → Appearance»
 */
final class AppearancePage
{
    public const SLUG = 'wheel-size-appearance';

    public function register(): void
    {
        add_action('admin_menu',        [$this, 'add_menu']);
        add_action('admin_init',        [$this, 'register_settings']);
        add_action('admin_enqueue_scripts', [$this, 'enqueue_assets']);

        // AJAX для Live-preview
        add_action('wp_ajax_wheel_size_render_preview', [$this, 'ajax_render_preview']);

        // AJAX для обновления дефолтных тем
        add_action('wp_ajax_wheel_size_update_default_themes', [$this, 'ajax_update_default_themes']);
    }

    /* ---------- Меню ---------- */
    public function add_menu(): void
    {
        add_submenu_page(
            'wheel-size',
            'Wheel-Size - Appearance',
            'Appearance',
            'manage_options',
            self::SLUG,
            [$this, 'render_page']
        );
    }

    /* ---------- Settings API ---------- */
    public function register_settings(): void
    {
        /* (!) Перенесли только «внешний» блок; остальные-то в своих модулях */
        register_setting('wheel_size_appearance_settings', 'wheel_size_primary_color');
        register_setting('wheel_size_appearance_settings', 'wheel_size_form_layout', [
            'type'              => 'string',
            'default'           => 'popup-horizontal',
            'sanitize_callback' => 'sanitize_key',
        ]);
        register_setting('wheel_size_appearance_settings', 'auto_search_on_last_input', [
            'type'              => 'boolean',
            'default'           => false,
            'sanitize_callback' => 'rest_sanitize_boolean',
        ]);
        register_setting('wheel_size_appearance_settings', 'wheel_size_show_by_vehicle', [
            'type'              => 'boolean',
            'default'           => true,
            'sanitize_callback' => 'rest_sanitize_boolean',
        ]);
        register_setting('wheel_size_appearance_settings', 'wheel_size_show_by_size', [
            'type'              => 'boolean',
            'default'           => true,
            'sanitize_callback' => 'rest_sanitize_boolean',
        ]);
        register_setting('wheel_size_appearance_settings', 'ws_search_flow', [
            'type'              => 'string',
            'default'           => 'by_generation',
            'sanitize_callback' => 'sanitize_key',
        ]);
        register_setting('wheel_size_appearance_settings', 'wheel_size_font_family', [
            'type'              => 'string',
            'default'           => 'system',
            'sanitize_callback' => 'sanitize_key',
        ]);
    }

    /* ---------- Assets ---------- */
    public function enqueue_assets(string $hook): void
    {
        if ($hook !== 'wheel-size_page_' . self::SLUG) {
            return;
        }

        /* тут копируем ровно тот кусок, что у вас был в Admin::enqueue_admin_assets
           c проверкой `$hook === 'wheel-size_page_wheel-size-appearance'` */

        // Tailwind CDN moved to Theme Presets section - only load when feature is enabled

        // Fonts CSS (load first to ensure fonts are available)
        $plugin_file_path = dirname(__DIR__, 2) . '/my-tyre-finder.php';
        $fonts_css_path = plugin_dir_path($plugin_file_path) . 'assets/css/fonts.css';
        if (file_exists($fonts_css_path)) {
            wp_enqueue_style(
                'wheel-fit-fonts',
                plugins_url('assets/css/fonts.css', $plugin_file_path),
                [],
                filemtime($fonts_css_path)
            );
        }

        // Google Fonts (load selected font family for admin preview)
        $this->enqueue_google_fonts();

        // Shared CSS (same as front-end)
        $shared_css_path = plugin_dir_path($plugin_file_path) . 'assets/css/wheel-fit-shared.css';
        if (file_exists($shared_css_path)) {
            wp_enqueue_style(
                'wheel-fit-shared-styles',
                plugins_url('assets/css/wheel-fit-shared.css', $plugin_file_path),
                ['wheel-fit-fonts'], // Depend on fonts being loaded first
                filemtime($shared_css_path)
            );
        }

        // Button Unification CSS (for Inline 1x4 and Step-by-Step layouts)
        $button_unification_css_path = plugin_dir_path($plugin_file_path) . 'assets/css/button-unification.css';
        if (file_exists($button_unification_css_path)) {
            wp_enqueue_style(
                'wheel-fit-button-unification',
                plugins_url('assets/css/button-unification.css', $plugin_file_path),
                ['wheel-fit-shared-styles'], // Load after shared styles
                filemtime($button_unification_css_path)
            );
        }

        // Live Preview Width Fix CSS (admin-specific)
        $width_fix_css_path = plugin_dir_path($plugin_file_path) . 'assets/css/live-preview-width-fix.css';
        if (file_exists($width_fix_css_path)) {
            wp_enqueue_style(
                'wheel-fit-live-preview-width-fix',
                plugins_url('assets/css/live-preview-width-fix.css', $plugin_file_path),
                ['wheel-fit-button-unification'], // Load after button unification
                filemtime($width_fix_css_path)
            );
        }

        // Enqueue WP Color Picker for manual initialization
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');

        // Register dynamic primary-color overrides (reuse front-end method)
        if(!wp_style_is('wheel-fit-preview-dynamic','registered')){
            wp_register_style('wheel-fit-preview-dynamic', false, [], '1.0.0');
        }
        // Generate dynamic CSS based on saved primary color and font
        $primary_color = get_option('wheel_size_primary_color', '#2563eb');
        $font_family = get_option('wheel_size_font_family', 'system');
        $font_css_value = ($font_family === 'system')
            ? "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
            : "'{$font_family}', sans-serif";

        wp_enqueue_style('wheel-fit-preview-dynamic');
        $dynamic_css = "
            :root {
                --wf-primary-color: {$primary_color};
                --wsf-font-base: {$font_css_value};
            }
            .wheel-fit-widget .bg-blue-600,
            .wheel-fit-widget .hover\\:bg-blue-700:hover,
            .wheel-fit-widget .border-blue-500:focus {
                background-color: var(--wf-primary-color) !important;
                border-color: var(--wf-primary-color) !important;
            }
            .wheel-fit-widget .text-blue-600 { color: var(--wf-primary-color) !important; }
            #step-1-indicator.bg-blue-600,
            #step-2-indicator.bg-blue-600,
            #step-3-indicator.bg-blue-600,
            #step-4-indicator.bg-blue-600 { background-color: var(--wf-primary-color) !important; }
        ";
        wp_add_inline_style('wheel-fit-preview-dynamic', $dynamic_css);

        // Additional dynamic styles (same as front-end)
        if (!wp_style_is('wheel-size-dynamic-style', 'registered')) {
            wp_register_style('wheel-size-dynamic-style', false);
        }
        wp_enqueue_style('wheel-size-dynamic-style');
        $this->output_dynamic_styles('wheel-size-dynamic-style');

        // JS & icons identical to front-end for fully functional preview
        wp_enqueue_script(
            'lucide-umd',
            'https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.min.js',
            [],
            '1.0.0',
            true
        );

        // Подключение скриптов виджета с правильными зависимостями (включая переводы)
        // (оставлен только нижний блок с wheel-fit-i18n)

        // Подключение переводов для скриптов виджета
        $active_locale = \MyTyreFinder\Translations\TranslationManager::active_locale();
        $i18n = \MyTyreFinder\Translations\TranslationManager::get_locale_data($active_locale);
        if ($active_locale !== 'en') {
            $i18n_en = \MyTyreFinder\Translations\TranslationManager::get_locale_data('en');
            $i18n = array_replace($i18n_en, $i18n);
        }

        // Регистрируем inline скрипт с переводами
        wp_register_script('wheel-fit-i18n', false, [], '1.0.0', true);
        wp_enqueue_script('wheel-fit-i18n');
        wp_add_inline_script('wheel-fit-i18n', 'window.WheelFitI18n = ' . wp_json_encode($i18n) . ';');

        // Подключение скриптов виджета с правильными зависимостями
        $layout = get_option('wheel_size_form_layout', 'popup-horizontal');
        $deps = ['lucide-umd', 'wheel-fit-i18n'];

        // Enqueue translation persistence manager (modeled after theme system)
        $translation_manager_path = plugin_dir_path($plugin_file_path) . 'assets/js/translation-persistence-manager.js';
        if (file_exists($translation_manager_path)) {
            wp_enqueue_script(
                'wheel-fit-translation-manager',
                plugins_url('assets/js/translation-persistence-manager.js', $plugin_file_path),
                ['wheel-fit-i18n'],
                filemtime($translation_manager_path),
                true
            );
            $deps[] = 'wheel-fit-translation-manager';
        }

        // Always enqueue both scripts on the appearance page for the preview
        $finder_js_path = plugin_dir_path($plugin_file_path) . 'assets/js/finder.js';
        if (file_exists($finder_js_path)) {
            wp_enqueue_script(
                'wheel-fit-script-admin-preview',
                plugins_url('assets/js/finder.js', $plugin_file_path),
                $deps,
                filemtime($finder_js_path),
                true
            );
        }

        $wizard_js_path = plugin_dir_path($plugin_file_path) . 'assets/js/wizard.js';
        if (file_exists($wizard_js_path)) {
            wp_enqueue_script(
                'wheel-fit-wizard-script-admin-preview',
                plugins_url('assets/js/wizard.js', $plugin_file_path),
                $deps,
                filemtime($wizard_js_path),
                true
            );
        }

        // Localize script data для обоих скриптов
        $is_garage_enabled = \MyTyreFinder\Admin\Admin::garage_enabled();
        $script_data = [
            'ajaxurl'       => admin_url('admin-ajax.php'),
            'nonce'         => wp_create_nonce('wheel_fit_nonce'),
            'apiUrl'        => home_url('/wp-json/wheel-fit/v1/'),
            'debug'         => defined('WP_DEBUG') && WP_DEBUG,
            'garageEnabled' => $is_garage_enabled,
            'autoSearch'    => (bool) get_option('auto_search_on_last_input', false),
            'formLayout'    => $layout,
        ];

        wp_localize_script('wheel-fit-script-admin-preview', 'WheelFitData', $script_data);
        wp_localize_script('wheel-fit-wizard-script-admin-preview', 'WheelFitData', $script_data);
        // Enqueue garage.js if garage feature is enabled
        if ($is_garage_enabled) {
            $garage_js_path = plugin_dir_path($plugin_file_path) . 'assets/js/garage.js';
            if (file_exists($garage_js_path)) {
                wp_enqueue_script(
                    'wheel-fit-garage-admin-preview',
                    plugins_url('assets/js/garage.js', $plugin_file_path),
                    ['wheel-fit-script-admin-preview', 'lucide-umd'],
                    filemtime($garage_js_path),
                    true
                );
                wp_localize_script('wheel-fit-garage-admin-preview', 'WheelFitData', $script_data);
            }
        }
        // Admin appearance specific JS
        $admin_appearance_js_path = plugin_dir_path($plugin_file_path) . 'assets/js/admin-appearance.js';
        wp_enqueue_script(
            'wheel-fit-admin-appearance',
            plugins_url('assets/js/admin-appearance.js', $plugin_file_path),
            ['jquery'],
            file_exists($admin_appearance_js_path) ? filemtime($admin_appearance_js_path) : '1.0.0',
            true
        );

        // Быстрое исправление селекторов
        $quick_fix_js_path = plugin_dir_path($plugin_file_path) . 'quick-selector-fix.js';
        if (file_exists($quick_fix_js_path)) {
            wp_add_inline_script('wheel-fit-admin-appearance', file_get_contents($quick_fix_js_path));
        }

        // Theme Presets Panel assets
        if (defined('WSF_THEME_PRESETS') && WSF_THEME_PRESETS) {
            // Tailwind CDN - only for Theme Presets feature
            wp_enqueue_script(
                'tailwind-cdn',
                'https://cdn.tailwindcss.com?plugins=forms,typography&preflight=false',
                [],
                '3.4.1',
                false
            );

            // Theme panel CSS
            $theme_panel_css_path = plugin_dir_path($plugin_file_path) . 'assets/css/admin-theme-panel.css';
            if (file_exists($theme_panel_css_path)) {
                wp_enqueue_style(
                    'wheel-fit-admin-theme-panel',
                    plugins_url('assets/css/admin-theme-panel.css', $plugin_file_path),
                    [],
                    filemtime($theme_panel_css_path)
                );
            }

            // Theme panel JS
            $theme_panel_js_path = plugin_dir_path($plugin_file_path) . 'assets/js/admin-theme-panel.js';
            if (file_exists($theme_panel_js_path)) {
                wp_enqueue_script(
                    'wheel-fit-admin-theme-panel',
                    plugins_url('assets/js/admin-theme-panel.js', $plugin_file_path),
                    ['jquery', 'wp-api-request'],
                    filemtime($theme_panel_js_path),
                    true
                );

                // Localize script with API settings
                wp_localize_script('wheel-fit-admin-theme-panel', 'wpApiSettings', [
                    'root' => esc_url_raw(rest_url()),
                    'nonce' => wp_create_nonce('wp_rest')
                ]);

                // Localize script with color tokens metadata
                wp_localize_script('wheel-fit-admin-theme-panel', 'wsfColorTokens', [
                    'tokens' => ColorTokens::get_tokens_for_js(),
                    'examples' => ColorTokens::get_examples()
                ]);

                // Add feature flag to window
                wp_add_inline_script('wheel-fit-admin-theme-panel', 'window.WSF_THEME_PRESETS = true;', 'before');
            }
        }

        // Add JavaScript for theme update button
        wp_add_inline_script('jquery', '
            jQuery(document).ready(function($) {
                // Add ajaxurl if not defined
                if (typeof ajaxurl === "undefined") {
                    window.ajaxurl = "' . admin_url('admin-ajax.php') . '";
                }

                // Styled notification function (same as theme system)
                function showStyledNotification(message, type = "info") {
                    const notification = document.createElement("div");
                    notification.className = `wsf-notification wsf-notification--${type}`;
                    notification.textContent = message;

                    document.body.appendChild(notification);

                    requestAnimationFrame(() => {
                        notification.classList.add("wsf-notification--show");
                    });

                    setTimeout(() => {
                        notification.classList.remove("wsf-notification--show");
                        setTimeout(() => {
                            if (notification.parentNode) {
                                notification.parentNode.removeChild(notification);
                            }
                        }, 300);
                    }, 3000);
                }

                $("#update-default-themes").on("click", function() {
                    var $btn = $(this);
                    var originalText = $btn.text();

                    $btn.prop("disabled", true).text("🔄 Resetting...");

                    $.ajax({
                        url: ajaxurl,
                        type: "POST",
                        dataType: "json",
                        data: {
                            action: "wheel_size_update_default_themes",
                            nonce: "' . wp_create_nonce('wheel_size_admin_nonce') . '"
                        },
                        success: function(response) {
                            console.log("AJAX Response:", response);
                            if (response && response.success) {
                                $btn.text("✅ Reset!");
                                showStyledNotification("Themes reset to defaults! The page will reload.", "success");
                                setTimeout(function() {
                                    location.reload();
                                }, 1500);
                            } else {
                                var errorMsg = response && response.data ? response.data : "Unknown error";
                                showStyledNotification("Error: " + errorMsg, "error");
                                $btn.prop("disabled", false).text(originalText);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error("AJAX Error:", xhr, status, error);
                            showStyledNotification("Network error: " + error, "error");
                            $btn.prop("disabled", false).text(originalText);
                        }
                    });
                });
            });
        ');
    }

    /* ---------- Рендер страницы ---------- */
    public function render_page(): void
    {
        if (isset($_POST['submit'])) {
            $this->save_settings();
        }

        $primary_color      = get_option('wheel_size_primary_color', '#2563eb');
        $layout_chosen      = get_option('wheel_size_form_layout', 'popup-horizontal');
        $show_by_vehicle    = get_option('wheel_size_show_by_vehicle', true);
        $show_by_size       = get_option('wheel_size_show_by_size', true);
        $auto_search        = get_option('auto_search_on_last_input', false);
        $search_flow        = get_option('ws_search_flow', 'by_generation');

        ?>
        <div class="wrap">
            <div class="flex items-center justify-between mb-4">
                <h1 class="mb-0">Wheel-Size - Appearance</h1>
            </div>

            <?php if (defined('WSF_THEME_PRESETS') && WSF_THEME_PRESETS): ?>
            <div class="mt-6 wsf-admin-grid">
                <div class="wsf-admin-grid__main">
                    <div class="flex flex-col gap-8">
                        <!-- Settings Card -->
                        <div class="flex-1 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <?php else: ?>
            <div class="mt-6">
                <div class="flex flex-col gap-8">
                    <!-- Settings Card -->
                    <div class="flex-1 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <?php endif; ?>
                        <form method="post" action="">
                            <?php wp_nonce_field('wheel_size_appearance_settings', 'wheel_size_nonce'); ?>
                            <h2 class="text-lg font-medium text-gray-900 mb-6 pb-4 border-b border-gray-200">Form Configuration</h2>

                            <div class="flex flex-col gap-6">
                                <!-- Primary Color field is now deprecated: visually hidden but kept in DOM for compatibility -->
                                <div class="flex items-center" style="display:none;">
                                    <label for="primary_color" class="w-48 shrink-0 text-sm font-medium text-gray-700">Primary Color</label>
                                    <input type="color" id="primary_color" name="primary_color" value="<?php echo esc_attr($primary_color); ?>" class="p-1 h-10 w-14 block bg-white border border-gray-300 rounded-md cursor-pointer">
                                </div>

                                <!-- Search Flow -->
                                <div class="flex items-start">
                                    <label for="search_flow" class="w-48 shrink-0 text-sm font-medium text-gray-700 pt-2">Search Flow</label>
                                    <div>
                                        <p class="text-xs text-gray-500 mb-2">Choose how a user will refine the vehicle before the search.<br>The full step sequence is shown below the selector.</p>
                                        <select id="search_flow" name="ws_search_flow" class="max-w-xs px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm focus:border-blue-500 focus:ring-blue-500">
                                            <option value="by_vehicle" <?php selected($search_flow, 'by_vehicle'); ?>>Make → Model → Year → Modification</option>
                                            <option value="by_year" <?php selected($search_flow, 'by_year'); ?>>Year → Make → Model → Modification</option>
                                            <option value="by_generation" <?php selected($search_flow, 'by_generation'); ?>>Make → Model → Generation → Modification</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- Form Layout -->
                                <div class="flex items-center">
                                    <label for="form_layout" class="w-48 shrink-0 text-sm font-medium text-gray-700">Form Layout</label>
                                    <select id="form_layout" name="form_layout" class="max-w-xs px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm focus:border-blue-500 focus:ring-blue-500">
                                        <?php
                                            $form_layouts = [
                                                'popup-horizontal' => __('Inline (1x4)', 'my-tyre-finder'),
                                                'inline'           => __('Grid (2x2)', 'my-tyre-finder'),
                                                'stepper'          => __('Step-by-Step', 'my-tyre-finder'),
                                                'wizard'           => __('Wizard', 'my-tyre-finder'),
                                            ];

                                            /* ------------------------------------------------------------------
                                             * Уже объявили popup‑horizontal первым, так что дополнительная
                                             * перестановка больше не нужна.
                                             * ------------------------------------------------------------------ */

                                            foreach ($form_layouts as $key => $label) {
                                                $is_wizard = ($key === 'wizard');
                                                $disable_wizard = ($is_wizard && ($search_flow === 'by_year' || $search_flow === 'by_generation'));
                                                ?>
                                                <option value="<?= esc_attr($key) ?>"
                                                        <?= selected($layout_chosen, $key, false) ?>
                                                        <?= $disable_wizard ? 'disabled' : '' ?>>
                                                    <?= esc_html($label) ?>
                                                </option>
                                                <?php
                                            }
                                        ?>
                                    </select>
                                </div>

                                <!-- Show Tabs - DISABLED: Force By Vehicle mode only -->
                                <?php /*
                                <div class="flex items-start">
                                    <label class="w-48 shrink-0 text-sm font-medium text-gray-700 pt-1">Show Tabs</label>
                                    <fieldset>
                                        <legend class="sr-only">Show tabs</legend>
                                        <div class="flex items-center gap-6">
                                            <label class="flex items-center gap-2 text-sm font-normal text-gray-700 whitespace-nowrap">
                                                <input type="checkbox" name="show_by_vehicle" value="1" <?php checked($show_by_vehicle); ?> class="rounded border-gray-300 text-blue-600 shadow-sm focus:ring-blue-500">
                                                By&nbsp;Vehicle
                                            </label>
                                            <label class="flex items-center gap-2 text-sm font-normal text-gray-700 whitespace-nowrap">
                                                <input type="checkbox" name="show_by_size" value="1" <?php checked($show_by_size); ?> class="rounded border-gray-300 text-blue-600 shadow-sm focus:ring-blue-500">
                                                By&nbsp;Size
                                            </label>
                                        </div>
                                        <p class="description text-xs text-gray-500 mt-2"></p>
                                    </fieldset>
                                </div>
                                */ ?>

                                <!-- Automatic Search -->
                                 <div class="flex items-center">
                                    <label class="w-48 shrink-0 text-sm font-medium text-gray-700">Automatic Search</label>
                                    <label class="inline-flex items-center gap-2 text-sm font-normal text-gray-700">
                                        <input type="checkbox" id="auto_search_on_last_input" name="auto_search_on_last_input" value="1" <?php checked($auto_search); ?> class="rounded border-gray-300 text-blue-600 shadow-sm focus:ring-blue-500">
                                        Automatically search on last input
                                    </label>
                                </div>

                                <!-- Font Family -->
                                <div class="flex items-center">
                                    <label for="font_family" class="w-48 shrink-0 text-sm font-medium text-gray-700">Font Family</label>
                                    <div class="flex-1">
                                        <select id="font_family" name="wheel_size_font_family" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                            <?php echo $this->render_font_options(); ?>
                                        </select>
                                        <p class="description text-xs text-gray-500 mt-2">Choose the font family for the search form and all layouts</p>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-8 pt-6 border-t border-gray-200">
                                <?php submit_button(__('Save Changes', 'wheel-size'), 'primary', 'submit', false); ?>
                            </div>
                        </form>
                    </div>

                    <!-- Live Preview Card -->
                    <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                        <h2 class="text-lg font-medium text-gray-900 mb-6 pb-4 border-b border-gray-200">Live Preview</h2>
                        <div id="widget-preview-wrapper" class="max-h-[calc(100vh-12rem)] overflow-y-auto">
                            <div id="widget-preview">
                                <?php
                                // Render form without extra wrapper to prevent duplication
                                require_once dirname(__DIR__,2) . '/src/public/Frontend.php';
                                $frontend = new \MyTyreFinder\Public\Frontend();
                                $rendered_content = $frontend->render_form_no_assets();

                                // Remove any duplicate wrapper containers that might cause nesting issues
                                // This prevents "form in form in form" effect in admin preview
                                echo $rendered_content;
                                ?>
                            </div>
                            <!-- Override WP-admin styles to match frontend appearance -->
                            <style>
                                /* Ensure widget title matches frontend styling in admin preview */
                                #widget-preview .wsf-widget__title {
                                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                                    font-size: 1.5rem !important; /* 24px */
                                    font-weight: 700 !important;
                                    line-height: 1.3 !important;
                                    text-align: center !important;
                                    margin: 0 !important;
                                    color: var(--wsf-text, #1f2937) !important;
                                }

                                /* Responsive title sizing to match frontend */
                                @media (min-width: 768px) {
                                    #widget-preview .wsf-widget__title {
                                        font-size: 1.875rem !important; /* 30px */
                                    }
                                }

                                /* Override WP-admin select width only inside preview */
                                #widget-preview select,
                                #widget-preview .wsf-input,
                                #widget-preview input[type="text"],
                                #widget-preview input[type="email"],
                                #widget-preview input[type="search"] {
                                    width: 100% !important;
                                    max-width: 100% !important;
                                    min-width: 0 !important;
                                    box-sizing: border-box !important;
                                }

                                /* Override WordPress admin form field containers */
                                #widget-preview .form-field,
                                #widget-preview .form-table td,
                                #widget-preview .form-wrap,
                                #widget-preview .wp-admin select {
                                    width: 100% !important;
                                    max-width: 100% !important;
                                }

                                /* Ensure flex containers don't constrain width */
                                #widget-preview .flex,
                                #widget-preview .flex-col,
                                #widget-preview .w-full {
                                    width: 100% !important;
                                    flex: 1 1 auto !important;
                                }

                                /* Ensure widget uses Inter font consistently */
                                #widget-preview .wheel-fit-widget,
                                #widget-preview .wheel-fit-widget *,
                                #widget-preview .wsf-finder-widget,
                                #widget-preview .wsf-finder-widget * {
                                    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                                }

                                /* Prevent wrapper duplication visual issues */
                                #widget-preview .wsf-form-wrapper .wsf-form-wrapper {
                                    background: transparent !important;
                                    border: none !important;
                                    padding: 0 !important;
                                    margin: 0 !important;
                                    box-shadow: none !important;
                                }

                                #widget-preview .wheel-fit-widget .wheel-fit-widget {
                                    background: transparent !important;
                                    border: none !important;
                                    padding: 0 !important;
                                    margin: 0 !important;
                                    box-shadow: none !important;
                                    border-radius: 0 !important;
                                }

                                /* Ensure only the outermost container has styling */
                                #widget-preview > div > .wheel-fit-widget:first-child {
                                    /* This is the main widget container */
                                }

                                #widget-preview > div > .wheel-fit-widget:not(:first-child) {
                                    /* Hide duplicate widget containers */
                                    display: none !important;
                                }

                                /* Note: Widget layout and width management moved to live-preview-width-fix.css */
                                /* This ensures consistent width matching with search results (56rem/896px) */
                            </style>
                        </div>
                    </div>
                    <?php if (defined('WSF_THEME_PRESETS') && WSF_THEME_PRESETS): ?>
                    </div>
                </div>

                <!-- Theme Presets Sidebar -->
                <div class="wsf-admin-grid__sidebar">
                    <div class="wsf-theme-panel">
                        <div class="wsf-theme-panel__header">
                            <h2 class="wsf-theme-panel__title text-lg font-medium text-gray-900 mb-2 pb-2 border-b border-gray-200"><?php esc_html_e('Theme Presets', 'wheel-size'); ?></h2>
                        </div>
                        <div class="wsf-theme-panel__content">
                            <!-- Theme cards will be loaded here by JavaScript -->
                            <div class="wsf-theme-panel__loader"></div>
                        </div>
                        <div class="wsf-theme-panel__footer mt-4 pt-3 border-t border-gray-100">
                            <button id="update-default-themes" class="wsf-reset-button text-xs text-gray-500 hover:text-gray-700 bg-transparent border-0 p-0 cursor-pointer underline decoration-dotted" type="button">
                                Reset to Default Themes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
                    <?php else: ?>
                </div>
            </div>
                    <?php endif; ?>
        </div>
        <script>
        document.addEventListener('DOMContentLoaded', function() {
            const layoutSelect = document.getElementById('form_layout');
            // Tab checkboxes no longer exist - Force By Vehicle mode only
            // const vehicleCheckbox = document.querySelector('[name="show_by_vehicle"]');
            // const sizeCheckbox = document.querySelector('[name="show_by_size"]');
            // const tabsRow = vehicleCheckbox.closest('.flex');
            // const description = tabsRow.querySelector('.description');
            const originalDescription = ''; // No default text
            const inlineOnlyDescription = 'Works only for the Inline (horizontal) layout.';

            const previewContainer = document.getElementById('widget-preview');
            const colorInput = document.getElementById('primary_color');
            const autoSearchCheckbox = document.getElementById('auto_search_on_last_input');
            const searchFlowSelect = document.getElementById('search_flow');
            const fontFamilySelect = document.getElementById('font_family');

    /* ---------- Wizard availability, tied to Search Flow ---------- */
    function enforceWizardAvailability() {
        if (!searchFlowSelect || !layoutSelect) return;
        const isYearFlow   = searchFlowSelect.value === 'by_year';
        const isGenerationFlow = searchFlowSelect.value === 'by_generation';
        const shouldDisableWizard = isYearFlow || isGenerationFlow;
        const wizardOption = layoutSelect.querySelector('option[value="wizard"]');
        if (wizardOption)  wizardOption.disabled = shouldDisableWizard;      // серый / активный

        // если Wizard был выбран – мгновенно переключаемся на Inline (1×4)
        if (shouldDisableWizard && layoutSelect.value === 'wizard') {
            layoutSelect.value = 'popup-horizontal';   // ← ваш Inline (1×4)
        }
    }

            const getPreviewContainer = () => document.getElementById('widget-preview');

            /* ---------- Fix Wrapper Duplication ---------- */
            function fixWrapperDuplication(container) {
                if (!container) return;

                console.log('[Admin Preview] Checking for wrapper duplication...');

                // Fix nested .wsf-form-wrapper elements
                const outerWrappers = container.querySelectorAll('.wsf-form-wrapper');
                outerWrappers.forEach(wrapper => {
                    const nestedWrappers = wrapper.querySelectorAll('.wsf-form-wrapper');
                    nestedWrappers.forEach(nested => {
                        console.log('[Admin Preview] Removing nested .wsf-form-wrapper');
                        // Move children to parent and remove nested wrapper
                        while (nested.firstChild) {
                            wrapper.insertBefore(nested.firstChild, nested);
                        }
                        nested.remove();
                    });
                });

                // Fix nested .wheel-fit-widget elements
                const outerWidgets = container.querySelectorAll('.wheel-fit-widget');
                outerWidgets.forEach(widget => {
                    const nestedWidgets = widget.querySelectorAll('.wheel-fit-widget');
                    nestedWidgets.forEach(nested => {
                        console.log('[Admin Preview] Removing nested .wheel-fit-widget');
                        // Move children to parent and remove nested widget
                        while (nested.firstChild) {
                            widget.insertBefore(nested.firstChild, nested);
                        }
                        nested.remove();
                    });
                });

                // Remove duplicate widget containers (keep only the first one)
                const allWidgets = container.querySelectorAll('.wheel-fit-widget, .wsf-finder-widget');
                if (allWidgets.length > 1) {
                    console.log(`[Admin Preview] Found ${allWidgets.length} widget containers, removing duplicates`);
                    for (let i = 1; i < allWidgets.length; i++) {
                        allWidgets[i].remove();
                    }
                }

                console.log('[Admin Preview] Wrapper duplication check complete');
            }

            const updatePreview = (scroll=false) => {
                const previewContainer = getPreviewContainer();
                if(!previewContainer) return;
                // All flows are now supported
                previewContainer.innerHTML = '<p>Loading preview…</p>';
                // Update color and font in-place as well (instant feedback)
                document.documentElement.style.setProperty('--wf-primary-color', colorInput.value);
                if (fontFamilySelect) {
                    const fontValue = fontFamilySelect.value === 'system'
                        ? "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
                        : `'${fontFamilySelect.value}', sans-serif`;
                    document.documentElement.style.setProperty('--wsf-font-base', fontValue);
                }

                const formData = new URLSearchParams({
                    action: 'wheel_size_render_preview',
                    layout: layoutSelect.value,
                    primary_color: colorInput.value,
                    font_family: fontFamilySelect ? fontFamilySelect.value : 'system',
                    show_by_vehicle: '1',  // Force By Vehicle mode
                    show_by_size: '',      // Force disable By Size mode
                    auto_search: autoSearchCheckbox.checked ? '1' : '',
                    search_flow: searchFlowSelect ? searchFlowSelect.value : '',
                    preview_locale: '<?php echo esc_js(\MyTyreFinder\Translations\TranslationManager::active_locale()); ?>'
                });
                fetch(ajaxurl, {
                    method: 'POST',
                    headers: {'Content-Type':'application/x-www-form-urlencoded'},
                    body: formData
                })
                .then(res=>res.ok?res.json():Promise.reject())
                .then(data=>{
                    if(data.success){
                        // ИСПОЛЬЗУЕМ ТОЧНО ТОТ ЖЕ КОД, ЧТО И В JAVASCRIPT ФАЙЛЕ!
                        // Обновляем переводы перед рендерингом (как в новом коде)
                        const newTranslations = data.data.i18n || {};
                        try {
                            window.WheelFitI18n = newTranslations;
                            console.log('[Admin Preview] Updated global translations:', Object.keys(window.WheelFitI18n).length, 'keys');
                            console.log('[Admin Preview] Sample translations:', {
                                select_make_placeholder: window.WheelFitI18n.select_make_placeholder,
                                select_model_placeholder: window.WheelFitI18n.select_model_placeholder,
                                select_year_placeholder: window.WheelFitI18n.select_year_placeholder
                            });
                        } catch(e) {
                            console.error('[Admin Preview] Error updating translations:', e);
                        }

                        // Вставляем новый HTML (как в новом коде)
                        previewContainer.innerHTML = data.data.html;
                        console.log('[Admin Preview] HTML updated');

                        // ПРОВЕРЯЕМ ПЕРЕВОДЫ ПЕРЕД ПРИМЕНЕНИЕМ
                        console.log('[Admin Preview] Checking translations before applying:', {
                            available: !!window.WheelFitI18n,
                            count: window.WheelFitI18n ? Object.keys(window.WheelFitI18n).length : 0,
                            sampleKey: window.WheelFitI18n ? window.WheelFitI18n.select_make_placeholder : 'N/A'
                        });

                        // Fix wrapper duplication before applying translations
                        fixWrapperDuplication(previewContainer);

                        // Применяем переводы к статическим элементам (как в новом коде)
                        if(typeof applyStaticTranslations === 'function') {
                            console.log('[Admin Preview] Applying translations...');
                            applyStaticTranslations(previewContainer);
                            console.log('[Admin Preview] Translations applied');
                        } else {
                            console.warn('[Admin Preview] applyStaticTranslations function not found');
                        }

                        // Инициализируем виджеты (как в новом коде)
                        if(typeof WheelFitWidget !== 'undefined'){
                            try{
                                window.wheelFitWidget = new WheelFitWidget();
                                console.log('[Admin Preview] WheelFitWidget initialized');
                            }catch(e){
                                console.error('[Admin Preview] Error initializing WheelFitWidget:', e);
                            }
                        }
                        if(typeof WheelWizard !== 'undefined'){
                            if(document.getElementById('wheel-fit-wizard')){
                                try{ window.wheelFitWizard = new WheelWizard(); }catch(e){ console.error(e); }
                            }
                        }

                        // Initialize garage if enabled and elements are present
                        if(window.WheelFitData && window.WheelFitData.garageEnabled) {
                            console.log('[Admin Preview] Garage enabled, checking for garage elements...');
                            const hasGarageElements = document.getElementById('garage-drawer') ||
                                                     document.getElementById('garage-overlay') ||
                                                     document.querySelectorAll('[data-garage-trigger]').length > 0;

                            if(hasGarageElements) {
                                console.log('[Admin Preview] Garage elements found, reinitializing...');
                                try {
                                    // Use reinit function if available, otherwise force init
                                    if (typeof window.reinitGarage === 'function') {
                                        window.reinitGarage();
                                    } else if (typeof initGarageFeature === 'function') {
                                        // Reset state manually and force reinit
                                        window.__garageInitiated = false;
                                        initGarageFeature(true);
                                    }
                                } catch(e) {
                                    console.error('[Admin Preview] Error reinitializing garage:', e);
                                }
                            } else {
                                console.log('[Admin Preview] Garage elements not found');
                            }
                        }
                        if(scroll){
                            previewContainer.scrollIntoView({behavior:'smooth',block:'start'});
                        }
                    }else{
                        previewContainer.innerHTML = '<p>Error loading preview</p>';
                    }
                })
                .catch(()=>{previewContainer.innerHTML = '<p>Error loading preview</p>';});
            };

            // Initial check (after full load to avoid race with assets)
            window.addEventListener('load', ()=>{
                // Fix wrapper duplication on initial load
                const initialPreviewContainer = document.getElementById('widget-preview');
                if(initialPreviewContainer) {
                    fixWrapperDuplication(initialPreviewContainer);
                }

                // Применяем переводы к начальной форме
                if(typeof applyStaticTranslations === 'function') {
                    if(initialPreviewContainer) {
                        console.log('[Initial Load] Applying translations to preview container');
                        applyStaticTranslations(initialPreviewContainer);
                    }
                }

                // Инициализируем виджеты с переводами
                if(typeof WheelFitWidget !== 'undefined'){
                    try{ window.wheelFitWidget = new WheelFitWidget(); }catch(e){ console.error(e); }
                }
                if(typeof WheelWizard !== 'undefined'){
                    if(document.getElementById('wheel-fit-wizard')){
                        try{ window.wheelFitWizard = new WheelWizard(); }catch(e){ console.error(e); }
                    }
                }

                enforceWizardAvailability();
                // Не вызываем updatePreview() при начальной загрузке, чтобы избежать дублирования
            });

            // Listen for changes
            layoutSelect.addEventListener('change', function() {
                updatePreview(true);
            });

            if (searchFlowSelect){
                searchFlowSelect.addEventListener('change', () => {
                    enforceWizardAvailability();
                    updatePreview(true);
                });
            }

            // Remove references to tab checkboxes - Force By Vehicle mode only
            [colorInput, autoSearchCheckbox, searchFlowSelect, fontFamilySelect].forEach(el=>{
                if(!el) return;
                el.addEventListener('change', updatePreview);
                if(el.type==='color') el.addEventListener('input', updatePreview);
            });

            function toggleShowTabs(){
                // Show Tabs section is now disabled - no longer needed
                // const isInline = layoutSelect.value === 'inline';
                // const showTabsRow = document.querySelector('[name="show_by_vehicle"]').closest('.flex.items-start');
                // if(showTabsRow){
                //     showTabsRow.style.display = isInline ? 'flex' : 'none';
                // }
            }

            // Initial check
            toggleShowTabs();

            // Listen for layout and flow changes
            layoutSelect.addEventListener('change', toggleShowTabs);
            if(searchFlowSelect){ searchFlowSelect.addEventListener('change', toggleShowTabs); }
        });
        </script>



        <?php
    }

    /* ---------- Сохранение ---------- */
    private function save_settings(): void
    {
        if (
            !isset($_POST['wheel_size_nonce']) ||
            !wp_verify_nonce($_POST['wheel_size_nonce'], 'wheel_size_appearance_settings')
        ) {
            wp_die('Permission check failed', 403);
        }

        update_option('wheel_size_primary_color', sanitize_hex_color($_POST['primary_color'] ?? '#2563eb'));
        $layout_chosen = sanitize_key($_POST['form_layout'] ?? 'popup-horizontal');

        // Save search flow variant
        $search_flow = isset($_POST['ws_search_flow']) ? sanitize_key($_POST['ws_search_flow']) : get_option('ws_search_flow', 'by_generation');
        update_option('ws_search_flow', $search_flow);

        // If by_year or by_generation flow is selected and wizard layout is chosen, fallback to inline
        if (($search_flow === 'by_year' || $search_flow === 'by_generation') && $layout_chosen === 'wizard') {
            $layout_chosen = 'popup-horizontal';   // Inline (1×4)
        }

        // If "none" (Disabled) was somehow selected, fallback to default layout
        if ($layout_chosen === 'none') {
            $layout_chosen = 'popup-horizontal';   // Inline (1×4)
        }

        update_option('wheel_size_form_layout', $layout_chosen);
        // Force By Vehicle mode only - don't save tab toggles from POST
        update_option('wheel_size_show_by_vehicle', true);  // Always enabled
        update_option('wheel_size_show_by_size', false);    // Always disabled
        // Save auto-search toggle (checkbox: true if checked, false if not)
        update_option('auto_search_on_last_input', !empty($_POST['auto_search_on_last_input']));
        // Save font family
        $font_family = isset($_POST['wheel_size_font_family']) ? sanitize_key($_POST['wheel_size_font_family']) : 'system';
        update_option('wheel_size_font_family', $font_family);
        // Maintain legacy active_flow option for compatibility (no UI)
        update_option('wheel_size_active_flow', $layout_chosen === 'wizard' ? 'flow1' : 'none');

        // Clear translation cache to ensure fresh translations are loaded
        TranslationManager::clear_cache();
    }

    /* ---------- AJAX preview ---------- */
    public function ajax_render_preview(): void
    {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('no-permission', 403);
        }

        $locale = $_POST['preview_locale'] ?? TranslationManager::active_locale();
        $locale = sanitize_key($locale);
        $locale_filter = function() use ($locale) { return $locale; };
        add_filter('locale', $locale_filter, 10, 0);

        // Clear translation cache for the preview locale to ensure fresh data
        TranslationManager::clear_cache($locale);

        $layout = isset($_POST['layout']) ? sanitize_key($_POST['layout']) : get_option('wheel_size_form_layout', 'popup-horizontal');
        $primary_color = isset($_POST['primary_color']) ? sanitize_hex_color($_POST['primary_color']) : get_option('wheel_size_primary_color', '#2563eb');
        $font_family = isset($_POST['font_family']) ? sanitize_key($_POST['font_family']) : get_option('wheel_size_font_family', 'system');
        // Force By Vehicle mode only - ignore POST and database settings
        $show_by_vehicle = true;   // Always enabled
        $show_by_size = false;     // Always disabled
        $auto_search = isset($_POST['auto_search']) ? (bool)$_POST['auto_search'] : get_option('auto_search_on_last_input', false);
        $search_flow = isset($_POST['search_flow']) ? sanitize_key($_POST['search_flow']) : get_option('ws_search_flow', 'by_generation');

        // Prepare callbacks for temporary option overrides
        $filters = [];
        $filters[] = ['option_wheel_size_form_layout', static fn() => $layout];
        // Ensure Wizard preview enables flow1, otherwise none
        $filters[] = ['option_wheel_size_active_flow', static fn() => $layout === 'wizard' ? 'flow1' : 'none'];
        $filters[] = ['option_wheel_size_primary_color', static fn() => $primary_color];
        $filters[] = ['option_wheel_size_font_family', static fn() => $font_family];
        $filters[] = ['option_wheel_size_show_by_vehicle', static fn() => true];   // Force enabled
        $filters[] = ['option_wheel_size_show_by_size', static fn() => false];     // Force disabled
        $filters[] = ['option_auto_search_on_last_input', static fn() => $auto_search];
        if (isset($_POST['search_flow'])) {
            $filters[] = ['option_ws_search_flow', static fn() => sanitize_key($_POST['search_flow'])];
        }

        foreach ($filters as [$tag, $cb]) {
            add_filter($tag, $cb, 10, 0);
        }

        try {
            // Используем метод без подключения скриптов для предотвращения дублирования
            $frontend = new \MyTyreFinder\Public\Frontend();
            $html = $frontend->render_form_no_assets();
            // Получаем словарь переводов для выбранной локали
            $dict = TranslationManager::get_locale_data($locale);
            if ($locale !== 'en') {
                $dict = array_replace(TranslationManager::get_locale_data('en'), $dict);
            }
            wp_send_json_success([
                'html' => $html,
                'i18n' => $dict,
                'locale' => $locale,
                'timestamp' => time() // Help with debugging
            ]);
        } catch (\Throwable $e) {
            wp_send_json_error($e->getMessage(), 500);
        } finally {
            // Remove temporary filters
            foreach ($filters as [$tag, $cb]) {
                remove_filter($tag, $cb, 10);
            }
            remove_filter('locale', $locale_filter, 10);
        }
    }

    /**
     * Generate CSS based on admin appearance settings and attach as inline style.
     */
    private function output_dynamic_styles(string $handle): void
    {
        $primary_color = get_option('wheel_size_primary_color', '#2563eb');
        $font_family = get_option('wheel_size_font_family', 'system');

        $font_css_value = ($font_family === 'system')
             ? "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
             : "'{$font_family}', sans-serif";

        // Collect active theme CSS variables so the preview exactly matches the front-end
        $theme_css_vars = '';
        if (defined('WSF_THEME_PRESETS') && WSF_THEME_PRESETS && class_exists('MyTyreFinder\\Includes\\ThemeManager')) {
            $props = \MyTyreFinder\Includes\ThemeManager::get_active_theme_properties();
            unset($props['name']);
            foreach ($props as $prop => $value) {
                if (str_starts_with($prop, '--')) {
                    $theme_css_vars .= "{$prop}: {$value};";
                }
            }
        }

        // Build CSS — use higher specificity to override Tailwind defaults
        $css = "
            :root {
                --wf-primary-color: {$primary_color};
                --wsf-font-base: {$font_css_value};
                {$theme_css_vars}
            }

            /* Ensure root helper picks the right family */
            .wsf-root-font { font-family: {$font_css_value} !important; }
            .wsf-root-font * { font-family: inherit !important; }

            /* Direct assignment fallback */
            .wheel-fit-widget { font-family: {$font_css_value}; {$theme_css_vars} }
            /* Primary background & text overrides */
            .wheel-fit-widget .bg-blue-600,
            .wheel-fit-widget .hover\\:bg-blue-700:hover,
            .wheel-fit-widget .border-blue-500:focus {
                background-color: var(--wf-primary-color) !important;
                border-color: var(--wf-primary-color) !important;
            }
            .wheel-fit-widget .text-blue-600 { color: var(--wf-primary-color) !important; }
            /* Step indicator active color */
            #step-1-indicator.bg-blue-600,
            #step-2-indicator.bg-blue-600,
            #step-3-indicator.bg-blue-600,
            #step-4-indicator.bg-blue-600 { background-color: var(--wf-primary-color) !important; }

            /* 🔒 Ensure modal overlay is always a semi-transparent dark backdrop */
            #garage-overlay { background-color: rgba(0,0,0,0.6) !important; }

            /* 🎨 Garage drawer modern styling */
            #garage-drawer {
                max-width: 420px !important;
                width: 100% !important;
                padding: 24px 20px !important;
                overflow-y: auto !important;
            }
            #garage-drawer article { margin-bottom: 12px !important; }
            #garage-drawer .garage-load {
                background-color: var(--wf-primary-color) !important;
                color: #fff !important;
                padding: 6px 14px !important;
                font-size: 0.75rem !important;
                font-weight: 600 !important;
                border-radius: 9999px !important;
                transition: background-color .15s ease, opacity .15s ease !important;
            }
            #garage-drawer .garage-load:hover:not(:disabled) { filter: brightness(110%); }
            #garage-drawer .garage-delete {
                color: var(--wsf-text-muted, #9ca3af) !important;
                width: 32px !important;
                height: 32px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                border-radius: 9999px !important;
                transition: background-color .15s ease, color .15s ease !important;
            }
            #garage-drawer .garage-delete:hover {
                background-color: rgba(0,0,0,0.05) !important;
                color: var(--wsf-error, #dc2626) !important;
            }
            #garage-drawer .garage-delete i { pointer-events: none; }

            /* 🎨 Garage Clear All button styling - red with thicker border */
            body.wp-admin #garage-drawer #garage-clear-all {
                background-color: transparent !important;
                background-image: none !important;
                color: #dc2626 !important;
                font-weight: 500 !important;
                font-size: 14px !important;
                font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif !important;
                padding: 8px 16px !important;
                border-radius: 8px !important;
                border: 2px solid #d1d5db !important;
                transition: all 0.15s ease !important;
                cursor: pointer !important;
                display: inline-flex !important;
                align-items: center !important;
                gap: 8px !important;
                text-decoration: none !important;
                box-shadow: none !important;
                outline: none !important;
                vertical-align: middle !important;
                line-height: 1.4 !important;
                white-space: nowrap !important;
                text-transform: none !important;
                letter-spacing: normal !important;
                word-spacing: normal !important;
                text-indent: 0 !important;
                text-shadow: none !important;
                text-align: center !important;
                margin-top: 0 !important;
            }
            body.wp-admin #garage-drawer #garage-clear-all:hover:not(:disabled) {
                background-color: #f9fafb !important;
                border-color: #dc2626 !important;
                color: #b91c1c !important;
                transform: none !important;
            }
            body.wp-admin #garage-drawer #garage-clear-all:focus {
                outline: none !important;
                box-shadow: 0 0 0 2px #dc2626, 0 0 0 4px rgba(220, 38, 38, 0.1) !important;
            }
            body.wp-admin #garage-drawer #garage-clear-all i {
                width: 16px !important;
                height: 16px !important;
                flex-shrink: 0 !important;
                margin: 0 !important;
            }

            /* 🎨 Garage footer spacing in admin - match frontend */
            body.wp-admin #garage-drawer #garage-footer {
                margin-top: 24px !important;
                padding-top: 20px !important;
                border-top: 1px solid var(--wsf-border-light, #e5e7eb) !important;
                position: static !important;
                bottom: auto !important;
            }

            /* 🎯 Compact circular garage count badge in admin */
            body.wp-admin .wsf-garage-count-badge {
                width: 18px !important;
                height: 18px !important;
                border-radius: 50% !important;
                font-size: 0.6875rem !important;
                font-weight: 600 !important;
                margin-left: 6px !important;
                display: inline-flex !important;
                align-items: center !important;
                justify-content: center !important;
                flex-shrink: 0 !important;
                line-height: 18px !important; /* Match height for perfect centering */
                text-align: center !important;
                box-sizing: border-box !important;
                padding: 0 !important;
                position: relative !important;
            }

            /* Fine-tune text positioning for perfect centering in admin */
            body.wp-admin .wsf-garage-count-badge::before {
                content: \'\' !important;
                display: inline-block !important;
                height: 100% !important;
                vertical-align: middle !important;
            }

            /* 🎨 Subtle reset button styling */
            .wsf-reset-button {
                font-size: 11px !important;
                color: #9CA3AF !important;
                background: transparent !important;
                border: none !important;
                padding: 0 !important;
                text-decoration: underline !important;
                text-decoration-style: dotted !important;
                cursor: pointer !important;
                transition: color 0.2s ease !important;
            }

            .wsf-reset-button:hover {
                color: #6B7280 !important;
                text-decoration-style: solid !important;
            }

            .wsf-theme-panel__footer {
                margin-top: 1rem !important;
                padding-top: 0.75rem !important;
                border-top: 1px solid #F3F4F6 !important;
                text-align: center !important;
            }

            /* Garage button alignment in admin */
            body.wp-admin [data-garage-trigger] {
                display: inline-flex !important;
                align-items: center !important;
                gap: 0.5rem !important;
            }

            body.wp-admin [data-garage-trigger] .wsf-garage-count-badge {
                margin-left: 2px !important;
            }



            /* 🧭 Fix top clipping & gap inside WP-Admin preview */
            body.wp-admin #garage-drawer {
                top: 0 !important;
                height: 100% !important;
            }

            /* 🎨 Force btn-secondary styles in admin context */
            body.wp-admin .btn-secondary,
            body.wp-admin button.btn-secondary,
            body.wp-admin #garage-clear-all {
                background-color: transparent !important;
                background-image: none !important;
                color: #ef4444 !important;
                font-weight: 500 !important;
                font-size: 14px !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                padding: 8px 12px !important;
                border-radius: 6px !important;
                border: 1px solid #f3f4f6 !important;
                transition: all 0.15s ease !important;
                cursor: pointer !important;
                display: inline-flex !important;
                align-items: center !important;
                gap: 6px !important;
                text-decoration: none !important;
                box-shadow: none !important;
                outline: none !important;
                vertical-align: middle !important;
                line-height: 1.4 !important;
                white-space: nowrap !important;
                text-transform: none !important;
                letter-spacing: normal !important;
                word-spacing: normal !important;
                text-indent: 0 !important;
                text-shadow: none !important;
                text-align: center !important;
            }

            body.wp-admin .btn-secondary:hover:not(:disabled),
            body.wp-admin button.btn-secondary:hover:not(:disabled),
            body.wp-admin #garage-clear-all:hover:not(:disabled) {
                background-color: #f3f4f6 !important;
                border-color: #ef4444 !important;
                color: #dc2626 !important;
                transform: none !important;
            }

            body.wp-admin .btn-secondary:focus,
            body.wp-admin button.btn-secondary:focus,
            body.wp-admin #garage-clear-all:focus {
                outline: none !important;
                box-shadow: 0 0 0 2px #ef4444, 0 0 0 4px rgba(239, 68, 68, 0.1) !important;
            }

            body.wp-admin .btn-secondary:disabled,
            body.wp-admin button.btn-secondary:disabled,
            body.wp-admin #garage-clear-all:disabled {
                opacity: 0.5 !important;
                cursor: not-allowed !important;
                pointer-events: none !important;
            }

            body.wp-admin .btn-secondary i,
            body.wp-admin button.btn-secondary i,
            body.wp-admin #garage-clear-all i {
                width: 16px !important;
                height: 16px !important;
                flex-shrink: 0 !important;
                margin: 0 !important;
            }

            /* 🎨 Ensure btn-secondary styles work in admin context */
            body.wp-admin .btn-secondary {
                background-color: transparent !important;
                color: var(--wsf-error, #ef4444) !important;
                font-weight: 500 !important;
                font-size: 0.875rem !important;
                padding: 0.5rem 0.75rem !important;
                border-radius: 0.375rem !important;
                border: 1px solid var(--wsf-border-light, #f3f4f6) !important;
                transition: all 0.15s ease !important;
                cursor: pointer !important;
                display: inline-flex !important;
                align-items: center !important;
                gap: 0.375rem !important;
                text-decoration: none !important;
                box-shadow: none !important;
            }

            body.wp-admin .btn-secondary:hover:not(:disabled) {
                background-color: var(--wsf-surface-hover, #f3f4f6) !important;
                border-color: var(--wsf-error, #ef4444) !important;
                color: var(--wsf-error, #dc2626) !important;
            }

            body.wp-admin .btn-secondary:focus {
                outline: none !important;
                box-shadow: 0 0 0 2px var(--wsf-error, #ef4444), 0 0 0 4px rgba(239, 68, 68, 0.1) !important;
            }

            body.wp-admin .btn-secondary:disabled {
                opacity: 0.5 !important;
                cursor: not-allowed !important;
                pointer-events: none !important;
            }

            body.wp-admin .btn-secondary i {
                width: 1rem !important;
                height: 1rem !important;
                flex-shrink: 0 !important;
            }
        ";

        wp_add_inline_style($handle, $css);
    }

    /**
     * AJAX handler for updating default themes with new accent colors
     */
    public function ajax_update_default_themes(): void
    {
        // Log the request for debugging
        error_log('AJAX update_default_themes called');

        if (!current_user_can('manage_options')) {
            error_log('Permission denied for user');
            wp_send_json_error('no-permission', 403);
        }

        if (!check_ajax_referer('wheel_size_admin_nonce', 'nonce', false)) {
            error_log('Invalid nonce');
            wp_send_json_error('invalid-nonce', 403);
        }

        try {
            error_log('Attempting to force update themes');

            // First clear existing themes
            delete_option('wsf_theme_presets');
            error_log('Cleared existing themes');

            // Force update default themes
            $result = ThemeManager::force_update_default_themes();
            error_log('Force update result: ' . ($result ? 'true' : 'false'));

            // Get updated themes to verify
            $themes = ThemeManager::get_themes();
            error_log('Retrieved themes: ' . count($themes) . ' themes found');

            $response = [
                'message' => 'Default themes updated successfully!',
                'themes' => [
                    'light' => [
                        'accent' => $themes['light']['--wsf-accent'] ?? 'not set'
                    ],
                    'dark' => [
                        'accent' => $themes['dark']['--wsf-accent'] ?? 'not set'
                    ]
                ]
            ];

            error_log('Sending success response: ' . json_encode($response));
            wp_send_json_success($response);

        } catch (Exception $e) {
            error_log('Exception in ajax_update_default_themes: ' . $e->getMessage());
            wp_send_json_error('Error: ' . $e->getMessage(), 500);
        } catch (Throwable $e) {
            error_log('Fatal error in ajax_update_default_themes: ' . $e->getMessage());
            wp_send_json_error('Fatal error: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Render font family options
     */
    private function render_font_options(): string
    {
        $current_font = get_option('wheel_size_font_family', 'system');

        $fonts = [
            'system' => 'System Default',
            'inter' => 'Inter',
            'roboto' => 'Roboto',
            'open-sans' => 'Open Sans',
            'lato' => 'Lato',
            'montserrat' => 'Montserrat',
            'poppins' => 'Poppins',
            'nunito' => 'Nunito',
            'source-sans-pro' => 'Source Sans Pro',
            'raleway' => 'Raleway',
            'ubuntu' => 'Ubuntu',
            'work-sans' => 'Work Sans',
            'playfair-display' => 'Playfair Display',
            'merriweather' => 'Merriweather',
            'pt-serif' => 'PT Serif',
            'crimson-text' => 'Crimson Text',
            'libre-baskerville' => 'Libre Baskerville',
            'oswald' => 'Oswald',
            'bebas-neue' => 'Bebas Neue',
            'anton' => 'Anton'
        ];

        $options = '';
        foreach ($fonts as $value => $label) {
            $selected = selected($current_font, $value, false);
            $options .= sprintf(
                '<option value="%s" %s>%s</option>',
                esc_attr($value),
                $selected,
                esc_html($label)
            );
        }

        return $options;
    }

    /**
     * Enqueue Google Fonts based on selected font family (for admin preview)
     */
    private function enqueue_google_fonts(): void
    {
        $font_family = get_option('wheel_size_font_family', 'system');

        // Skip if system font is selected
        if ($font_family === 'system') {
            return;
        }

        // Map font keys to Google Fonts URLs
        $google_fonts = [
            'inter' => 'Inter:wght@300;400;500;600;700',
            'roboto' => 'Roboto:wght@300;400;500;700',
            'open-sans' => 'Open+Sans:wght@300;400;600;700',
            'lato' => 'Lato:wght@300;400;700',
            'montserrat' => 'Montserrat:wght@300;400;500;600;700',
            'poppins' => 'Poppins:wght@300;400;500;600;700',
            'nunito' => 'Nunito:wght@300;400;600;700',
            'source-sans-pro' => 'Source+Sans+Pro:wght@300;400;600;700',
            'raleway' => 'Raleway:wght@300;400;500;600;700',
            'ubuntu' => 'Ubuntu:wght@300;400;500;700',
            'work-sans' => 'Work+Sans:wght@300;400;500;600;700',
            'playfair-display' => 'Playfair+Display:wght@400;500;600;700',
            'merriweather' => 'Merriweather:wght@300;400;700',
            'pt-serif' => 'PT+Serif:wght@400;700',
            'crimson-text' => 'Crimson+Text:wght@400;600',
            'libre-baskerville' => 'Libre+Baskerville:wght@400;700',
            'oswald' => 'Oswald:wght@300;400;500;600;700',
            'bebas-neue' => 'Bebas+Neue:wght@400',
            'anton' => 'Anton:wght@400'
        ];

        if (isset($google_fonts[$font_family])) {
            wp_enqueue_style(
                'wheel-size-google-font-admin',
                'https://fonts.googleapis.com/css2?family=' . $google_fonts[$font_family] . '&display=swap',
                [],
                null
            );
        }
    }
}