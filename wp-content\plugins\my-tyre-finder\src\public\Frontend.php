<?php

declare(strict_types=1);

namespace MyTyreFinder\Public;

use Twig\Environment;
use Twig\Loader\FilesystemLoader;
use MyTyreFinder\Admin\ApiValidator;
use MyTyreFinder\Translations\TranslationManager;
use MyTyreFinder\Includes\ThemeManager;

/**
 * Handles front-end assets and widget registration.
 */
final class Frontend
{
    private Environment $templateRenderer;

    public function __construct()
    {
        $templatePath = trailingslashit(dirname(__DIR__, 2)) . 'templates';
        $loader = new FilesystemLoader($templatePath);
        $this->templateRenderer = new Environment($loader, [
            'cache' => false, // Disable cache for development
        ]);
    }

    public function register(): void
    {
        add_action('wp_enqueue_scripts', [$this, 'enqueue_assets'], 20);

        // Critical button styles - load everywhere (frontend and admin)
        add_action('wp_head', [$this, 'output_critical_button_styles'], 1);
        add_action('admin_head', [$this, 'output_critical_button_styles'], 1);

        // Provide shortcode to render the form.
        add_shortcode('tyre_finder', [$this, 'render_form']);
        add_shortcode('wheel_fit', [$this, 'render_form']); // Alternative shortcode name

        // Force By Vehicle mode only - override database settings
        add_filter('option_wheel_size_show_by_vehicle', [$this, 'force_by_vehicle_mode']);
        add_filter('option_wheel_size_show_by_size', [$this, 'force_disable_by_size_mode']);
    }

    /**
     * Enqueue plugin assets.
     */
    public function enqueue_assets(): void
    {
        // Don't load scripts in admin or AJAX requests to avoid conflicts
        if (is_admin() || wp_doing_ajax()) {
            return;
        }
        // --- 0. i18n dictionary needed before loading any JS ----------------
        $active_locale = TranslationManager::active_locale();
        $i18n = TranslationManager::get_locale_data($active_locale);
        if ($active_locale !== 'en') {
            $i18n_en = TranslationManager::get_locale_data('en');
            $i18n = array_replace($i18n_en, $i18n);
        }

        // 1. Tailwind CDN
        wp_enqueue_script(
            'tailwind-cdn',
            'https://cdn.tailwindcss.com?plugins=forms,typography&preflight=false',
            [], '3.4.1', false
        );

        // 2. Fonts CSS (load first to ensure fonts are available)
        $plugin_file_path = dirname(__DIR__, 2) . '/my-tyre-finder.php';
        $fonts_css_path = plugin_dir_path($plugin_file_path) . 'assets/css/fonts.css';
        if (file_exists($fonts_css_path)) {
            wp_enqueue_style(
                'wheel-fit-fonts',
                plugins_url('assets/css/fonts.css', $plugin_file_path),
                [],
                filemtime($fonts_css_path)
            );
        }

        // 3. Shared CSS
        $shared_css_path = plugin_dir_path($plugin_file_path) . 'assets/css/wheel-fit-shared.css';
        if (file_exists($shared_css_path)) {
            wp_enqueue_style(
                'wheel-fit-shared-styles',
                plugins_url('assets/css/wheel-fit-shared.css', $plugin_file_path),
                ['wheel-fit-fonts'], // Depend on fonts being loaded first
                filemtime($shared_css_path)
            );
        }

        // 3.1. Button Unification CSS (for Inline 1x4 and Step-by-Step layouts)
        $button_unification_css_path = plugin_dir_path($plugin_file_path) . 'assets/css/button-unification.css';
        if (file_exists($button_unification_css_path)) {
            wp_enqueue_style(
                'wheel-fit-button-unification',
                plugins_url('assets/css/button-unification.css', $plugin_file_path),
                ['wheel-fit-shared-styles'], // Load after shared styles
                filemtime($button_unification_css_path)
            );
        }

        // 4. Dynamic Primary Color Styles
        if (!wp_style_is('wheel-size-dynamic-style', 'registered')) {
            wp_register_style('wheel-size-dynamic-style', false);
        }
        wp_enqueue_style('wheel-size-dynamic-style');
        $this->output_dynamic_styles('wheel-size-dynamic-style');

        // 5. Lucide Icons
        wp_enqueue_script(
            'lucide-umd',
            'https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.min.js',
            [], '1.0.0', true
        );

        // 5. Widget JS Logic (Wizard or Finder)
        $layout = get_option('wheel_size_form_layout', 'popup-horizontal');
        $handle = 'wheel-fit-script';
        
        if ($layout === 'wizard') {
            $handle = 'wheel-fit-wizard-script';
            $script_path = plugin_dir_path($plugin_file_path) . 'assets/js/wizard.js';
            $script_url = plugins_url('assets/js/wizard.js', $plugin_file_path);
        } else {
            $script_path = plugin_dir_path($plugin_file_path) . 'assets/js/finder.js';
            $script_url = plugins_url('assets/js/finder.js', $plugin_file_path);
        }

        if (file_exists($script_path)) {
            wp_enqueue_script(
                $handle,
                $script_url,
                ['lucide-umd'],
                filemtime($script_path),
                true
            );
        }

        // 5.5. Enqueue translation persistence manager for frontend consistency
        $translation_manager_path = plugin_dir_path($plugin_file_path) . 'assets/js/translation-persistence-manager.js';
        if (file_exists($translation_manager_path)) {
            wp_enqueue_script(
                'wheel-fit-translation-manager',
                plugins_url('assets/js/translation-persistence-manager.js', $plugin_file_path),
                ['wheel-fit-i18n'],
                filemtime($translation_manager_path),
                true
            );
        }

        // 6. Localize script data (common set)
        $is_garage_enabled = \MyTyreFinder\Admin\Admin::garage_enabled();
        $features = get_option('wheel_size_features', ['enable_oe_filter' => false]);
        $enable_oe_filter = (bool) ($features['enable_oe_filter'] ?? false);

        wp_localize_script($handle, 'WheelFitData', [
            'ajaxurl'       => admin_url('admin-ajax.php'),
            'nonce'         => wp_create_nonce('wheel_fit_nonce'),
            'apiUrl'        => home_url('/wp-json/wheel-fit/v1/'),
            'debug'         => defined('WP_DEBUG') && WP_DEBUG,
            'garageEnabled' => $is_garage_enabled,
            'autoSearch'    => (bool) get_option('auto_search_on_last_input', false),
            'formLayout'    => $layout,
            'locale'        => \MyTyreFinder\Translations\TranslationManager::active_locale(),
            'enableOeFilter' => $enable_oe_filter,
        ]);
        wp_localize_script($handle, 'WheelFitI18n', $i18n);

        // 7. Garage JS
        if ($is_garage_enabled) {
            $garage_js_path = plugin_dir_path($plugin_file_path) . 'assets/js/garage.js';
            if (file_exists($garage_js_path)) {
                wp_enqueue_script(
                    'wheel-size-garage',
                    plugins_url('assets/js/garage.js', $plugin_file_path),
                    [$handle, 'lucide-umd'],
                    filemtime($garage_js_path),
                    true
                );
            }
        }
    }

    /**
     * Generate CSS based on admin appearance settings and attach as inline style.
     */
    private function output_dynamic_styles(string $handle): void
    {
        $primary_color = get_option('wheel_size_primary_color', '#2563eb');
        $font_family = get_option('wheel_size_font_family', 'Inter');

        $font_css_value = ($font_family === 'system')
            ? "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
            : "'{$font_family}', sans-serif";

        // Retrieve active theme CSS variables (if Theme Presets feature enabled)
        $theme_css_vars = '';
        if (defined('WSF_THEME_PRESETS') && WSF_THEME_PRESETS && class_exists(ThemeManager::class)) {
            $props = ThemeManager::get_active_theme_properties();
            // Remove the human-readable name if present
            unset($props['name']);
            foreach ($props as $prop => $value) {
                if (str_starts_with($prop, '--')) {
                    $theme_css_vars .= "{$prop}: {$value};";
                }
            }
        }

        // Build CSS — use higher specificity to override Tailwind defaults
        $css = "
            :root {
                --wf-primary-color: {$primary_color};
                /* Expose selected font to the CSS token system */
                --wsf-font-base: {$font_css_value};
                {$theme_css_vars}
            }

            /* Ensure the universal root font helper uses the selected family */
            .wsf-root-font { font-family: {$font_css_value} !important; }
            .wsf-root-font * { font-family: inherit !important; }

            /* Fallback for legacy widgets that rely on direct font-family assignment */
            .wheel-fit-widget,
            #garage-drawer,
            #garage-overlay,
            #garage-toast {
                font-family: {$font_css_value};
            }

            /* Apply active theme design tokens */
            .wheel-fit-widget {
                {$theme_css_vars}
            }

            /* Ensure garage elements inherit font properly */
            #garage-drawer *,
            #garage-overlay *,
            #garage-toast * { font-family: inherit; }

            /* Primary background & text overrides */
            .wheel-fit-widget .bg-blue-600,
            .wheel-fit-widget .hover\\:bg-blue-700:hover,
            .wheel-fit-widget .border-blue-500:focus,
            #garage-drawer .bg-blue-600,
            #garage-toast.bg-blue-600 {
                background-color: var(--wf-primary-color) !important;
                border-color: var(--wf-primary-color) !important;
            }
            .wheel-fit-widget .text-blue-600,
            #garage-drawer .text-blue-600 { color: var(--wf-primary-color) !important; }

            /* Step indicator active color */
            #step-1-indicator.bg-blue-600,
            #step-2-indicator.bg-blue-600,
            #step-3-indicator.bg-blue-600,
            #step-4-indicator.bg-blue-600 { background-color: var(--wf-primary-color) !important; }

            /* Garage specific styling fixes */
            #garage-drawer .garage-load.bg-indigo-50 {
                background-color: rgba(99, 102, 241, 0.1) !important;
            }
            #garage-drawer .garage-load.text-indigo-600 {
                color: rgb(99, 102, 241) !important;
            }
            #garage-drawer .garage-load:hover.bg-indigo-100 {
                background-color: rgba(99, 102, 241, 0.2) !important;
            }

            /* 🔒 Ensure modal overlay is always a semi-transparent dark backdrop */
            #garage-overlay {
                background-color: rgba(0,0,0,0.6) !important;
            }

            /* 🎨 Garage drawer modern styling */
            #garage-drawer {
                max-width: 420px !important; /* more comfortable width */
                width: 100% !important;
                padding: 24px 20px !important;
                overflow-y: auto !important;
                top: 0 !important; /* Reset top position for frontend */
                height: 100vh !important; /* Full height on frontend */
            }
            #garage-drawer article {
                margin-bottom: 12px !important;
            }
            #garage-drawer .garage-load {
                background-color: var(--wf-primary-color) !important;
                color: #fff !important;
                padding: 6px 14px !important;
                font-size: 0.75rem !important; /* text-xs */
                font-weight: 600 !important;
                border-radius: 9999px !important; /* fully rounded */
                transition: background-color .15s ease, opacity .15s ease !important;
            }
            #garage-drawer .garage-load:hover:not(:disabled) {
                filter: brightness(110%);
            }
            #garage-drawer .garage-delete {
                color: var(--wsf-text-muted, #9ca3af) !important;
                width: 32px !important;
                height: 32px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                border-radius: 9999px !important;
                transition: background-color .15s ease, color .15s ease !important;
            }
            #garage-drawer .garage-delete:hover {
                background-color: rgba(0,0,0,0.05) !important;
                color: var(--wsf-error, #dc2626) !important;
            }
            #garage-drawer .garage-delete i { pointer-events: none; }

            /* 🔧 Frontend-specific garage positioning fixes */
            body:not(.wp-admin) #garage-drawer {
                top: 0 !important;
                height: 100vh !important;
            }

            /* Override admin bar styles on frontend */
            body:not(.admin-bar) #garage-drawer {
                top: 0 !important;
                height: 100vh !important;
            }

            /* 🎨 Garage Clear All button styling - red with thicker border */
            #garage-drawer #garage-clear-all {
                background-color: transparent !important;
                background-image: none !important;
                color: var(--wsf-error, #dc2626) !important;
                font-weight: 500 !important;
                font-size: 14px !important;
                font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif !important;
                padding: 8px 16px !important;
                border-radius: 8px !important;
                border: 2px solid var(--wsf-border-light, #d1d5db) !important;
                transition: all 0.15s ease !important;
                cursor: pointer !important;
                display: inline-flex !important;
                align-items: center !important;
                gap: 8px !important;
                text-decoration: none !important;
                box-shadow: none !important;
                outline: none !important;
                vertical-align: middle !important;
                line-height: 1.4 !important;
                white-space: nowrap !important;
                text-transform: none !important;
                letter-spacing: normal !important;
                word-spacing: normal !important;
                text-indent: 0 !important;
                text-shadow: none !important;
                text-align: center !important;
                margin-top: 16px !important;
            }
            #garage-drawer #garage-clear-all:hover:not(:disabled) {
                background-color: var(--wsf-surface-hover, #f9fafb) !important;
                border-color: var(--wsf-error, #dc2626) !important;
                color: var(--wsf-error, #b91c1c) !important;
                transform: none !important;
            }
            #garage-drawer #garage-clear-all:focus {
                outline: none !important;
                box-shadow: 0 0 0 2px var(--wsf-error, #dc2626), 0 0 0 4px rgba(220, 38, 38, 0.1) !important;
            }
            #garage-drawer #garage-clear-all i {
                width: 16px !important;
                height: 16px !important;
                flex-shrink: 0 !important;
                margin: 0 !important;
            }

            /* 🎨 Ensure btn-secondary styles work on frontend */
            .btn-secondary,
            button.btn-secondary,
            #garage-clear-all {
                background-color: transparent !important;
                background-image: none !important;
                color: #ef4444 !important;
                font-weight: 500 !important;
                font-size: 14px !important;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
                padding: 8px 12px !important;
                border-radius: 6px !important;
                border: 1px solid #f3f4f6 !important;
                transition: all 0.15s ease !important;
                cursor: pointer !important;
                display: inline-flex !important;
                align-items: center !important;
                gap: 6px !important;
                text-decoration: none !important;
                box-shadow: none !important;
                outline: none !important;
                vertical-align: middle !important;
                line-height: 1.4 !important;
                white-space: nowrap !important;
                text-transform: none !important;
                letter-spacing: normal !important;
                word-spacing: normal !important;
                text-indent: 0 !important;
                text-shadow: none !important;
                text-align: center !important;
            }

            .btn-secondary:hover:not(:disabled),
            button.btn-secondary:hover:not(:disabled),
            #garage-clear-all:hover:not(:disabled) {
                background-color: #f3f4f6 !important;
                border-color: #ef4444 !important;
                color: #dc2626 !important;
                transform: none !important;
            }

            .btn-secondary:focus,
            button.btn-secondary:focus,
            #garage-clear-all:focus {
                outline: none !important;
                box-shadow: 0 0 0 2px #ef4444, 0 0 0 4px rgba(239, 68, 68, 0.1) !important;
            }

            .btn-secondary:disabled,
            button.btn-secondary:disabled,
            #garage-clear-all:disabled {
                opacity: 0.5 !important;
                cursor: not-allowed !important;
                pointer-events: none !important;
            }

            .btn-secondary i,
            button.btn-secondary i,
            #garage-clear-all i {
                width: 16px !important;
                height: 16px !important;
                flex-shrink: 0 !important;
                margin: 0 !important;
            }

            /* Responsive styles for mobile devices */
            @media (max-width: 640px) {
                .btn-secondary,
                button.btn-secondary,
                #garage-clear-all {
                    padding: 8px !important;
                    min-width: 40px !important;
                }

                .btn-secondary span,
                button.btn-secondary span,
                #garage-clear-all span {
                    display: none !important;
                }

                .btn-secondary i,
                button.btn-secondary i,
                #garage-clear-all i {
                    margin: 0 !important;
                }
            }


        ";

        wp_add_inline_style($handle, $css);
    }

    /**
     * Output critical button styles directly to head - works everywhere
     */
    public function output_critical_button_styles(): void
    {
        // Debug: Add comment to verify this method is called
        echo '<!-- Wheel-Fit Critical Button Styles Loaded at ' . date('H:i:s') . ' -->';
        echo '<style id="wheel-fit-critical-button-styles">
            /* 🎨 Critical btn-secondary styles - maximum specificity */
            .btn-secondary,
            button.btn-secondary,
            #garage-clear-all {
                background-color: transparent !important;
                background-image: none !important;
                color: #ef4444 !important;
                font-weight: 500 !important;
                font-size: 14px !important;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
                padding: 8px 12px !important;
                border-radius: 6px !important;
                border: 1px solid #f3f4f6 !important;
                transition: all 0.15s ease !important;
                cursor: pointer !important;
                display: inline-flex !important;
                align-items: center !important;
                gap: 6px !important;
                text-decoration: none !important;
                box-shadow: none !important;
                outline: none !important;
                vertical-align: middle !important;
                line-height: 1.4 !important;
                white-space: nowrap !important;
                text-transform: none !important;
                letter-spacing: normal !important;
                word-spacing: normal !important;
                text-indent: 0 !important;
                text-shadow: none !important;
                text-align: center !important;
            }

            .btn-secondary:hover:not(:disabled),
            button.btn-secondary:hover:not(:disabled),
            #garage-clear-all:hover:not(:disabled) {
                background-color: #f3f4f6 !important;
                border-color: #ef4444 !important;
                color: #dc2626 !important;
                transform: none !important;
            }

            .btn-secondary:focus,
            button.btn-secondary:focus,
            #garage-clear-all:focus {
                outline: none !important;
                box-shadow: 0 0 0 2px #ef4444, 0 0 0 4px rgba(239, 68, 68, 0.1) !important;
            }

            .btn-secondary:disabled,
            button.btn-secondary:disabled,
            #garage-clear-all:disabled {
                opacity: 0.5 !important;
                cursor: not-allowed !important;
                pointer-events: none !important;
            }

            .btn-secondary i,
            button.btn-secondary i,
            #garage-clear-all i {
                width: 16px !important;
                height: 16px !important;
                flex-shrink: 0 !important;
                margin: 0 !important;
            }

            /* Responsive styles for mobile devices */
            @media (max-width: 640px) {
                .btn-secondary,
                button.btn-secondary,
                #garage-clear-all {
                    padding: 8px !important;
                    min-width: 40px !important;
                }

                .btn-secondary span,
                button.btn-secondary span,
                #garage-clear-all span {
                    display: none !important;
                }

                .btn-secondary i,
                button.btn-secondary i,
                #garage-clear-all i {
                    margin: 0 !important;
                }
            }
        </style>';
    }

    /**
     * Get critical button styles as inline string (for embedding in shortcode)
     */
    private function get_critical_button_styles_inline(): string
    {
        return '<style>
            /* 🎨 Critical btn-secondary styles - embedded with widget */
            .btn-secondary,
            button.btn-secondary,
            #garage-clear-all {
                background-color: transparent !important;
                background-image: none !important;
                color: #ef4444 !important;
                font-weight: 500 !important;
                font-size: 14px !important;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
                padding: 8px 12px !important;
                border-radius: 6px !important;
                border: 1px solid #f3f4f6 !important;
                transition: all 0.15s ease !important;
                cursor: pointer !important;
                display: inline-flex !important;
                align-items: center !important;
                gap: 6px !important;
                text-decoration: none !important;
                box-shadow: none !important;
                outline: none !important;
                vertical-align: middle !important;
                line-height: 1.4 !important;
                white-space: nowrap !important;
                text-transform: none !important;
                letter-spacing: normal !important;
                word-spacing: normal !important;
                text-indent: 0 !important;
                text-shadow: none !important;
                text-align: center !important;
            }

            .btn-secondary:hover:not(:disabled),
            button.btn-secondary:hover:not(:disabled),
            #garage-clear-all:hover:not(:disabled) {
                background-color: #f3f4f6 !important;
                border-color: #ef4444 !important;
                color: #dc2626 !important;
                transform: none !important;
            }

            .btn-secondary:focus,
            button.btn-secondary:focus,
            #garage-clear-all:focus {
                outline: none !important;
                box-shadow: 0 0 0 2px #ef4444, 0 0 0 4px rgba(239, 68, 68, 0.1) !important;
            }

            .btn-secondary:disabled,
            button.btn-secondary:disabled,
            #garage-clear-all:disabled {
                opacity: 0.5 !important;
                cursor: not-allowed !important;
                pointer-events: none !important;
            }

            .btn-secondary i,
            button.btn-secondary i,
            #garage-clear-all i {
                width: 16px !important;
                height: 16px !important;
                flex-shrink: 0 !important;
                margin: 0 !important;
            }

            /* Garage Clear All button - red with thicker border */
            #garage-drawer #garage-clear-all {
                background-color: transparent !important;
                background-image: none !important;
                color: #dc2626 !important;
                font-weight: 500 !important;
                font-size: 14px !important;
                font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif !important;
                padding: 8px 16px !important;
                border-radius: 8px !important;
                border: 2px solid #d1d5db !important;
                transition: all 0.15s ease !important;
                cursor: pointer !important;
                display: inline-flex !important;
                align-items: center !important;
                gap: 8px !important;
                text-decoration: none !important;
                box-shadow: none !important;
                outline: none !important;
                vertical-align: middle !important;
                line-height: 1.4 !important;
                white-space: nowrap !important;
                text-transform: none !important;
                letter-spacing: normal !important;
                word-spacing: normal !important;
                text-indent: 0 !important;
                text-shadow: none !important;
                text-align: center !important;
            }
            #garage-drawer #garage-clear-all:hover:not(:disabled) {
                background-color: #f9fafb !important;
                border-color: #dc2626 !important;
                color: #b91c1c !important;
                transform: none !important;
            }
            #garage-drawer #garage-clear-all:focus {
                outline: none !important;
                box-shadow: 0 0 0 2px #dc2626, 0 0 0 4px rgba(220, 38, 38, 0.1) !important;
            }
            #garage-drawer #garage-clear-all i {
                width: 16px !important;
                height: 16px !important;
                flex-shrink: 0 !important;
                margin: 0 !important;
            }

            /* Frontend garage positioning fixes */
            body:not(.wp-admin) #garage-drawer,
            body:not(.admin-bar) #garage-drawer {
                top: 0 !important;
                height: 100vh !important;
            }

            /* Garage footer and clear button spacing */
            #garage-drawer #garage-footer {
                margin-top: 24px !important;
                padding-top: 20px !important;
                border-top: 1px solid var(--wsf-border-light, #e5e7eb) !important;
                position: static !important; /* Override sticky positioning */
            }
            #garage-drawer #garage-clear-all {
                margin-top: 0 !important;
            }

            /* 🎯 Compact circular garage count badge */
            .wsf-garage-count-badge {
                width: 18px !important;
                height: 18px !important;
                border-radius: 50% !important;
                font-size: 0.6875rem !important;
                font-weight: 600 !important;
                margin-left: 6px !important;
                display: inline-flex !important;
                align-items: center !important;
                justify-content: center !important;
                flex-shrink: 0 !important;
                line-height: 18px !important; /* Match height for perfect centering */
                text-align: center !important;
                box-sizing: border-box !important;
                padding: 0 !important;
                position: relative !important;
            }

            /* Fine-tune text positioning for perfect centering */
            .wsf-garage-count-badge::before {
                content: \'\' !important;
                display: inline-block !important;
                height: 100% !important;
                vertical-align: middle !important;
            }

            /* Garage button alignment */
            [data-garage-trigger] {
                display: inline-flex !important;
                align-items: center !important;
                gap: 0.5rem !important;
            }

            [data-garage-trigger] .wsf-garage-count-badge {
                margin-left: 2px !important;
            }

            /* Responsive styles for mobile devices */
            @media (max-width: 640px) {
                .btn-secondary,
                button.btn-secondary,
                #garage-clear-all {
                    padding: 8px !important;
                    min-width: 40px !important;
                }

                .btn-secondary span,
                button.btn-secondary span,
                #garage-clear-all span {
                    display: none !important;
                }

                .btn-secondary i,
                button.btn-secondary i,
                #garage-clear-all i {
                    margin: 0 !important;
                }
            }
        </style>';
    }

    /**
     * Render the tyre finder form via Twig template.
     */
    public function render_form(): string
    {
        // Check if API is configured before rendering
        if (!ApiValidator::is_api_configured()) {
            return $this->render_api_not_configured_message();
        }

        try {
            $widget_title = TranslationManager::get('widget_title') ?: 'Wheel-Size Finder';
            $primary_color = get_option('wheel_size_primary_color', '#2563eb');
            $garage_enabled = \MyTyreFinder\Admin\Admin::garage_enabled();
            // Force By Vehicle mode only - ignore database settings
            $show_by_vehicle = true;   // Always enabled
            $show_by_size = false;     // Always disabled
            $features = get_option('wheel_size_features', [
                'enable_oe_filter' => false,
            ]);
            $enable_oe_filter = (bool) ($features['enable_oe_filter'] ?? false);
            $active_flow = get_option('wheel_size_active_flow', 'none');
            if ($active_flow === 'none' && get_option('wheel_size_form_layout') === 'wizard' && get_option('ws_search_flow', 'by_generation') === 'by_generation') {
                $active_flow = 'flow1';
            }
            // Choose template based on admin setting (stepper by default)
            $form_layout = get_option('wheel_size_form_layout', 'popup-horizontal');

            // If "none" (Disabled) was somehow selected, fallback to default layout
            if ($form_layout === 'none') {
                $form_layout = 'popup-horizontal';   // Inline (1×4)
            }

            if ($active_flow === 'flow1') {
                $template = 'wizard-flow.twig';
                $flow_order = null; // not needed for wizard
            } else {
                if ($form_layout === 'inline') {
                    $template = 'finder-form-inline.twig';
                } elseif ($form_layout === 'popup-horizontal') {
                    $template = 'finder-popup-horizontal.twig';
                } else {
                    $template = 'finder-form.twig';
                }
                
                // Set flow order based on search flow setting
                $search_flow = get_option('ws_search_flow', 'by_generation');
                if ($search_flow === 'by_year') {
                    $flow_order = ['year', 'make', 'model', 'mod'];
                } elseif ($search_flow === 'by_generation') {
                    $flow_order = ['make', 'model', 'gen', 'mod']; // New generation-based flow
                } else {
                    $flow_order = ['make', 'model', 'year', 'mod']; // Legacy flow order for by_vehicle
                }
                
                // ⬅️ NEW: для stepper-layout переключаемся на динамический шаблон
                if ($form_layout === 'stepper' && $search_flow !== 'by_model') {
                    $template = 'finder-form-flow.twig';
                }
            }

            $rendered_content = $this->templateRenderer->render($template, [
                'ajax_url'      => admin_url('admin-ajax.php'),
                'nonce'         => wp_create_nonce('tyre_finder_nonce'),
                'widget_title'  => $widget_title,
                'show_logo'     => false,
                'logo_url'      => '',
                'primary_color' => $primary_color,
                'garage_enabled' => $garage_enabled,
                'show_by_vehicle' => $show_by_vehicle,
                'show_by_size' => $show_by_size,
                'plugin_url'    => plugins_url('', dirname(__DIR__, 2) . '/my-tyre-finder.php'),
                'form_layout'   => $form_layout,
                'auto_search'   => (bool) get_option('auto_search_on_last_input', false),
                'enable_oe_filter' => $enable_oe_filter,
                'flow_order'    => $flow_order,
                'active_flow'   => $active_flow,
                'flow_order_json' => $flow_order ? json_encode($flow_order) : 'null',
            ]);

            // Ensure critical button styles are included with the widget
            $critical_styles = $this->get_critical_button_styles_inline();

            return $critical_styles . $rendered_content;
        } catch (\Exception $e) {
            return '<p>Error rendering finder form: ' . esc_html($e->getMessage()) . '</p>';
        }
    }

    /**
     * Render form without enqueuing assets (for AJAX preview)
     */
    public function render_form_no_assets(): string
    {
        return $this->render_form();
    }

    /**
     * Alias for render_form() to match expected method name.
     */
    public function renderFinder(): string
    {
        return $this->render_form();
    }

    /**
     * Force By Vehicle mode to always be enabled
     */
    public function force_by_vehicle_mode($value): bool
    {
        return true;
    }

    /**
     * Force By Size mode to always be disabled
     */
    public function force_disable_by_size_mode($value): bool
    {
        return false;
    }

    /**
     * Render message when API is not configured
     */
    private function render_api_not_configured_message(): string
    {
        $message = ApiValidator::get_unconfigured_message();
        $admin_link = '';

        // Only show admin link to users who can manage options
        if (current_user_can('manage_options')) {
            $api_settings_url = ApiValidator::get_api_settings_url();
            $admin_link = ' <a href="' . esc_url($api_settings_url) . '">Configure API Settings</a>';
        }

        return '<div class="wheel-fit-widget api-not-configured" style="
            padding: 20px;
            border: 2px solid #dc3232;
            border-radius: 8px;
            background-color: #fff2f2;
            color: #dc3232;
            text-align: center;
            font-family: -apple-system, BlinkMacSystemFont, \'Segoe UI\', Roboto, sans-serif;
        ">
            <div style="font-size: 18px; font-weight: bold; margin-bottom: 8px;">
                ⚠️ Plugin Not Configured
            </div>
            <div style="font-size: 14px;">
                This feature is unavailable because a valid API key has not been set.<br><br>
                To activate the plugin, go to the "API" tab and enter your API key from <a href="https://developer.wheel-size.com" target="_blank" rel="noopener" style="color: #dc3232; text-decoration: underline;">developer.wheel-size.com</a>.<br><br>
                Once validated, this page will become fully functional.' . $admin_link . '
            </div>
        </div>';
    }
}