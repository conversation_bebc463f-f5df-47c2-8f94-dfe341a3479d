<?php
declare(strict_types=1);

namespace MyTyreFinder\Includes;

/**
 * Theme Manager - handles CRUD operations for theme presets
 */
final class ThemeManager
{
    private const OPTION_THEMES = 'wsf_theme_presets';
    private const OPTION_ACTIVE_THEME = 'wsf_active_theme';
    private const DEFAULT_THEME = 'light';

    /**
     * Get all theme presets
     *
     * @return array Associative array of themes [slug => properties]
     */
    public static function get_themes(): array
    {
        $themes = get_option(self::OPTION_THEMES, []);

        // Ensure default themes exist
        if (empty($themes)) {
            $themes = self::get_default_themes();
            update_option(self::OPTION_THEMES, $themes, false);
        }

        // Check if default themes need updating (compare primary colors)
        $needs_update = false;
        $new_defaults = self::get_default_themes();

        if (isset($themes['light']) && isset($new_defaults['light'])) {
            $current_light_primary = $themes['light']['--wsf-primary'] ?? '';
            $new_light_primary = $new_defaults['light']['--wsf-primary'] ?? '';
            if ($current_light_primary !== $new_light_primary) {
                $needs_update = true;
            }
        }

        if (isset($themes['dark']) && isset($new_defaults['dark'])) {
            $current_dark_primary = $themes['dark']['--wsf-primary'] ?? '';
            $new_dark_primary = $new_defaults['dark']['--wsf-primary'] ?? '';
            if ($current_dark_primary !== $new_dark_primary) {
                $needs_update = true;
            }
        }

        // Auto-update default themes if needed
        if ($needs_update) {
            // Preserve custom themes
            $custom_themes = [];
            foreach ($themes as $slug => $theme_data) {
                if (!in_array($slug, ['light', 'dark'], true)) {
                    $custom_themes[$slug] = $theme_data;
                }
            }

            // Merge updated defaults with custom themes
            $themes = array_merge($new_defaults, $custom_themes);
            update_option(self::OPTION_THEMES, $themes, false);

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ThemeManager::get_themes - Auto-updated default themes with new color schemes");
            }
        }

        // Migrate themes with non-ASCII slugs
        $themes = self::migrate_non_ascii_slugs($themes);

        return $themes;
    }

    /**
     * Get a specific theme by slug
     * 
     * @param string $slug Theme slug
     * @return array|null Theme properties or null if not found
     */
    public static function get_theme(string $slug): ?array
    {
        $themes = self::get_themes();
        return $themes[$slug] ?? null;
    }

    /**
     * Get the active theme slug
     * 
     * @return string Active theme slug
     */
    public static function get_active_theme(): string
    {
        return get_option(self::OPTION_ACTIVE_THEME, self::DEFAULT_THEME);
    }

    /**
     * Get the active theme properties
     * 
     * @return array Active theme properties
     */
    public static function get_active_theme_properties(): array
    {
        $active_slug = self::get_active_theme();
        $theme = self::get_theme($active_slug);
        
        // Fallback to default theme if active theme not found
        if (!$theme) {
            $theme = self::get_theme(self::DEFAULT_THEME);
        }
        
        return $theme ?? self::get_default_themes()[self::DEFAULT_THEME];
    }

    /**
     * Set the active theme
     * 
     * @param string $slug Theme slug
     * @return bool Success status
     */
    public static function set_active_theme(string $slug): bool
    {
        $themes = self::get_themes();

        // Debug logging for theme activation issues
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("ThemeManager::set_active_theme - Attempting to activate theme: {$slug}");
            error_log("ThemeManager::set_active_theme - Available themes: " . implode(', ', array_keys($themes)));
        }

        if (!isset($themes[$slug])) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ThemeManager::set_active_theme - Theme '{$slug}' not found in available themes");
            }
            return false;
        }

        // Perform the update (returns true if option value changed)
        update_option(self::OPTION_ACTIVE_THEME, $slug);

        // Verify that the option is now set to the requested slug. This protects
        // against cases where update_option returns false because the value was
        // already set, which previously caused a false negative.
        $is_active = get_option(self::OPTION_ACTIVE_THEME) === $slug;

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("ThemeManager::set_active_theme - Activation verification: " . ($is_active ? 'success' : 'failed'));
        }

        return $is_active;
    }

    /**
     * Create a new theme
     * 
     * @param string $name Theme name
     * @param array $properties CSS custom properties
     * @return string|false Theme slug on success, false on failure
     */
    public static function create_theme(string $name, array $properties)
    {
        $slug = self::generate_slug($name);
        $themes = self::get_themes();
        
        // Ensure unique slug
        $original_slug = $slug;
        $counter = 1;
        while (isset($themes[$slug])) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }
        
        // Validate and sanitize properties
        $sanitized_properties = self::sanitize_theme_properties($properties);
        if (empty($sanitized_properties)) {
            return false;
        }
        
        // Add name to properties for easier management
        $sanitized_properties['name'] = sanitize_text_field($name);
        
        $themes[$slug] = $sanitized_properties;
        
        if (update_option(self::OPTION_THEMES, $themes, false)) {
            return $slug;
        }
        
        return false;
    }

    /**
     * Update an existing theme
     * 
     * @param string $slug Theme slug
     * @param string $name New theme name
     * @param array $properties New CSS custom properties
     * @return bool Success status
     */
    public static function update_theme(string $slug, string $name, array $properties): bool
    {
        $themes = self::get_themes();
        
        if (!isset($themes[$slug])) {
            return false;
        }
        
        // Validate and sanitize properties
        $sanitized_properties = self::sanitize_theme_properties($properties);
        if (empty($sanitized_properties)) {
            return false;
        }
        
        // Add name to properties
        $sanitized_properties['name'] = sanitize_text_field($name);
        
        $themes[$slug] = $sanitized_properties;
        
        return update_option(self::OPTION_THEMES, $themes, false);
    }

    /**
     * Duplicate a theme
     * 
     * @param string $source_slug Source theme slug
     * @param string $new_name New theme name
     * @return string|false New theme slug on success, false on failure
     */
    public static function duplicate_theme(string $source_slug, string $new_name)
    {
        $source_theme = self::get_theme($source_slug);
        
        if (!$source_theme) {
            return false;
        }
        
        // Remove name from properties to avoid duplication
        $properties = $source_theme;
        unset($properties['name']);
        
        return self::create_theme($new_name, $properties);
    }

    /**
     * Delete a theme
     * 
     * @param string $slug Theme slug
     * @return bool Success status
     */
    public static function delete_theme(string $slug): bool
    {
        // Prevent deletion of default themes
        if (in_array($slug, ['light', 'dark'], true)) {
            return false;
        }
        
        $themes = self::get_themes();
        
        if (!isset($themes[$slug])) {
            return false;
        }
        
        // If deleting active theme, switch to default
        if (self::get_active_theme() === $slug) {
            self::set_active_theme(self::DEFAULT_THEME);
        }
        
        unset($themes[$slug]);
        
        return update_option(self::OPTION_THEMES, $themes, false);
    }

    /**
     * Generate ASCII-only slug from theme name with international character support
     *
     * @param string $name Theme name
     * @return string Generated ASCII slug
     */
    private static function generate_slug(string $name): string
    {
        // Handle empty or whitespace-only names
        $name = trim($name);
        if (empty($name)) {
            return 'custom-theme';
        }

        // Use WordPress built-in sanitize_title function as primary method
        $slug = sanitize_title($name);

        // If sanitize_title returns empty (can happen with pure non-Latin text),
        // fall back to transliteration approach
        if (empty($slug)) {
            // Convert to lowercase
            $slug = mb_strtolower($name, 'UTF-8');

            // Basic transliteration for common non-Latin characters
            $transliterations = [
                // Cyrillic (Russian)
                'а' => 'a', 'б' => 'b', 'в' => 'v', 'г' => 'g', 'д' => 'd',
                'е' => 'e', 'ё' => 'yo', 'ж' => 'zh', 'з' => 'z', 'и' => 'i',
                'й' => 'y', 'к' => 'k', 'л' => 'l', 'м' => 'm', 'н' => 'n',
                'о' => 'o', 'п' => 'p', 'р' => 'r', 'с' => 's', 'т' => 't',
                'у' => 'u', 'ф' => 'f', 'х' => 'h', 'ц' => 'ts', 'ч' => 'ch',
                'ш' => 'sh', 'щ' => 'sch', 'ъ' => '', 'ы' => 'y', 'ь' => '',
                'э' => 'e', 'ю' => 'yu', 'я' => 'ya',
                // German
                'ä' => 'ae', 'ö' => 'oe', 'ü' => 'ue', 'ß' => 'ss',
                // French/Spanish/Portuguese
                'à' => 'a', 'á' => 'a', 'â' => 'a', 'ã' => 'a', 'ç' => 'c',
                'è' => 'e', 'é' => 'e', 'ê' => 'e', 'ë' => 'e',
                'ì' => 'i', 'í' => 'i', 'î' => 'i', 'ï' => 'i',
                'ñ' => 'n', 'ò' => 'o', 'ó' => 'o', 'ô' => 'o', 'õ' => 'o',
                'ù' => 'u', 'ú' => 'u', 'û' => 'u', 'ü' => 'u',
                'ý' => 'y', 'ÿ' => 'y'
            ];

            $slug = strtr($slug, $transliterations);

            // Replace spaces and common punctuation with hyphens
            $slug = preg_replace('/[\s\.,;:!?\'"()[\]{}№]+/', '-', $slug);

            // Remove any remaining non-ASCII characters and replace with hyphens
            $slug = preg_replace('/[^\x20-\x7E]/', '-', $slug);

            // Clean up the slug - only allow alphanumeric and hyphens
            $slug = preg_replace('/[^a-z0-9\-]/', '-', $slug);

            // Clean up multiple hyphens and trim
            $slug = preg_replace('/-+/', '-', $slug);
            $slug = trim($slug, '-');
        }

        // Final fallback if everything failed
        if (empty($slug)) {
            $slug = 'theme-' . substr(md5($name), 0, 8);
        }

        // Ensure slug is not purely numeric to avoid DB conflicts and routing issues
        if (preg_match('/^\d+$/', $slug)) {
            $slug = 'theme-' . $slug;
        }

        // Replace underscores with hyphens to enforce kebab-case
        $slug = str_replace('_', '-', $slug);

        // Final validation - ensure only ASCII alphanumeric and hyphens
        $slug = preg_replace('/[^a-zA-Z0-9\-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');

        // Ensure we have a valid slug
        if (empty($slug)) {
            $slug = 'custom-theme';
        }

        return $slug;
    }

    /**
     * Migrate themes with non-ASCII slugs to ASCII-only slugs
     *
     * @param array $themes Current themes array
     * @return array Migrated themes array
     */
    private static function migrate_non_ascii_slugs(array $themes): array
    {
        $migrated_themes = [];
        $needs_update = false;
        $active_theme = self::get_active_theme();
        $new_active_theme = $active_theme;

        foreach ($themes as $slug => $theme_data) {
            // Check if slug contains non-ASCII characters
            if (!preg_match('/^[a-zA-Z0-9\-]+$/', $slug)) {
                // Generate new ASCII slug from theme name
                $theme_name = $theme_data['name'] ?? $slug;
                $new_slug = self::generate_slug($theme_name);

                // Ensure unique slug
                $original_new_slug = $new_slug;
                $counter = 1;
                while (isset($migrated_themes[$new_slug]) || ($new_slug !== $slug && isset($themes[$new_slug]))) {
                    $new_slug = $original_new_slug . '-' . $counter;
                    $counter++;
                }

                // Migrate the theme
                $migrated_themes[$new_slug] = $theme_data;
                $needs_update = true;

                // Update active theme if necessary
                if ($active_theme === $slug) {
                    $new_active_theme = $new_slug;
                }

                // Log migration for debugging
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("ThemeManager::migrate_non_ascii_slugs - Migrated theme '{$slug}' to '{$new_slug}'");
                }
            } else {
                // Keep existing ASCII slug
                $migrated_themes[$slug] = $theme_data;
            }
        }

        // Update database if migration occurred
        if ($needs_update) {
            update_option(self::OPTION_THEMES, $migrated_themes, false);

            // Update active theme if it was migrated
            if ($new_active_theme !== $active_theme) {
                update_option(self::OPTION_ACTIVE_THEME, $new_active_theme);

                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("ThemeManager::migrate_non_ascii_slugs - Updated active theme from '{$active_theme}' to '{$new_active_theme}'");
                }
            }

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ThemeManager::migrate_non_ascii_slugs - Migration completed, updated database");
            }
        }

        return $migrated_themes;
    }

    /**
     * Force update default themes with new color schemes
     * This method will replace existing light and dark themes with updated versions
     * while preserving any custom themes
     *
     * @return bool Success status
     */
    public static function force_update_default_themes(): bool
    {
        try {
            $current_themes = get_option(self::OPTION_THEMES, []);
            $new_default_themes = self::get_default_themes();

            // Preserve custom themes (anything that's not 'light' or 'dark')
            $custom_themes = [];
            foreach ($current_themes as $slug => $theme_data) {
                if (!in_array($slug, ['light', 'dark'], true)) {
                    $custom_themes[$slug] = $theme_data;
                }
            }

            // Merge new defaults with existing custom themes
            $updated_themes = array_merge($new_default_themes, $custom_themes);

            // Update the database
            $result = update_option(self::OPTION_THEMES, $updated_themes, false);

            // Clear any caches
            wp_cache_delete(self::OPTION_THEMES, 'options');

            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ThemeManager::force_update_default_themes - Updated default themes");
            }

            return $result;

        } catch (Exception $e) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ThemeManager::force_update_default_themes - Error: " . $e->getMessage());
            }
            return false;
        }
    }

    /**
     * Sanitize theme properties
     *
     * @param array $properties Raw properties
     * @return array Sanitized properties
     */
    private static function sanitize_theme_properties(array $properties): array
    {
        $sanitized = [];
        $allowed_properties = [
            '--wsf-primary',
            '--wsf-bg',
            '--wsf-text',
            '--wsf-border',
            '--wsf-hover',
            '--wsf-secondary',
            '--wsf-accent',
            '--wsf-muted',
            '--wsf-success',
            '--wsf-warning',
            '--wsf-error',
            // Additional properties for better theme support
            '--wsf-text-primary',
            '--wsf-text-secondary',
            '--wsf-text-muted',
            '--wsf-on-primary',
            '--wsf-surface',
            '--wsf-input-bg',
            '--wsf-input-text',
            '--wsf-input-border',
            '--wsf-surface-hover',
            '--wsf-focus-ring'
        ];

        // Debug logging for theme property validation
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("ThemeManager::sanitize_theme_properties - Input properties: " . json_encode($properties));
        }

        foreach ($properties as $key => $value) {
            // Skip name property as it's handled separately
            if ($key === 'name') {
                continue;
            }

            // Only allow CSS custom properties
            if (!in_array($key, $allowed_properties, true)) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("ThemeManager::sanitize_theme_properties - Skipping disallowed property: {$key}");
                }
                continue;
            }

            // Enhanced color validation - support more formats
            $color = self::validate_color_value($value);
            if ($color) {
                $sanitized[$key] = $color;
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("ThemeManager::sanitize_theme_properties - Accepted {$key}: {$color}");
                }
            } else {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("ThemeManager::sanitize_theme_properties - Rejected {$key}: {$value}");
                }
            }
        }

        // Ensure we have at least one valid property
        if (empty($sanitized)) {
            if (defined('WP_DEBUG') && WP_DEBUG) {
                error_log("ThemeManager::sanitize_theme_properties - No valid properties found, adding defaults");
            }

            // Add minimal default properties to prevent complete failure
            $sanitized['--wsf-primary'] = '#2563eb';
            $sanitized['--wsf-bg'] = '#ffffff';
            $sanitized['--wsf-text'] = '#1f2937';
        }

        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("ThemeManager::sanitize_theme_properties - Output properties: " . json_encode($sanitized));
        }

        return $sanitized;
    }

    /**
     * Enhanced color validation supporting multiple formats
     *
     * @param string $value Color value to validate
     * @return string|false Sanitized color or false if invalid
     */
    private static function validate_color_value(string $value): string|false
    {
        // Trim whitespace
        $value = trim($value);

        if (empty($value)) {
            return false;
        }

        // Try WordPress built-in hex color validation first
        $hex_color = sanitize_hex_color($value);
        if ($hex_color) {
            return $hex_color;
        }

        // Support 3-character hex colors (e.g., #f00)
        if (preg_match('/^#[0-9a-fA-F]{3}$/', $value)) {
            // Convert 3-char hex to 6-char hex
            $expanded = '#' . $value[1] . $value[1] . $value[2] . $value[2] . $value[3] . $value[3];
            return strtolower($expanded);
        }

        // Support RGB/RGBA values (basic validation)
        if (preg_match('/^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(?:,\s*[\d.]+\s*)?\)$/i', $value)) {
            return $value; // Return as-is for CSS compatibility
        }

        // Support HSL/HSLA values (basic validation)
        if (preg_match('/^hsla?\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*(?:,\s*[\d.]+\s*)?\)$/i', $value)) {
            return $value; // Return as-is for CSS compatibility
        }

        // Support CSS custom properties (variables)
        if (preg_match('/^var\(--[\w-]+\)$/', $value)) {
            return $value;
        }

        return false;
    }

    /**
     * Get default theme definitions with modern, minimalistic color schemes
     *
     * @return array Default themes
     */
    private static function get_default_themes(): array
    {
        return [
            'light' => [
                'name' => __('Light Theme', 'wheel-size'),
                // Modern light theme with clean, professional colors
                // Background and base colors - Pure white with subtle grays
                '--wsf-bg' => '#FFFFFF',              // Pure white for maximum cleanliness
                '--wsf-surface' => '#FAFBFC',         // Very light gray for cards/surfaces
                '--wsf-surface-hover' => '#F1F3F5',   // Slightly darker for hover states

                // Text colors with excellent contrast (WCAG AA compliant)
                '--wsf-text' => '#1A1D23',            // Near-black for primary text (contrast ratio 16.7:1)
                '--wsf-text-primary' => '#1A1D23',    // Same as main text
                '--wsf-text-secondary' => '#6B7280',  // Medium gray for secondary text (contrast ratio 5.8:1)
                '--wsf-text-muted' => '#9CA3AF',      // Light gray for muted text (contrast ratio 3.9:1)

                // Primary brand colors - Modern blue with automotive feel
                '--wsf-primary' => '#2563EB',         // Vibrant blue (contrast ratio 7.5:1 on white)
                '--wsf-hover' => '#1D4ED8',           // Darker blue for hover states
                '--wsf-accent' => '#60A5FA',          // Soft blue accent for garage button (blue-400)
                '--wsf-on-primary' => '#FFFFFF',      // White text on primary buttons

                // Border and divider colors
                '--wsf-border' => '#E5E7EB',          // Light gray borders
                '--wsf-secondary' => '#D1D5DB',       // Medium gray for secondary elements
                '--wsf-muted' => '#F3F4F6',           // Very light gray for backgrounds

                // Input field colors
                '--wsf-input-bg' => '#FFFFFF',        // White input backgrounds
                '--wsf-input-text' => '#1A1D23',      // Dark text in inputs
                '--wsf-input-border' => '#D1D5DB',    // Gray input borders
                '--wsf-input-placeholder' => '#9CA3AF', // Gray placeholder text
                '--wsf-input-focus' => '#2563EB',     // Blue focus color

                // Status colors with automotive context
                '--wsf-success' => '#059669',         // Forest green for success (contrast ratio 6.4:1)
                '--wsf-warning' => '#D97706',         // Amber for warnings (contrast ratio 5.9:1)
                '--wsf-error' => '#DC2626',           // Red for errors (contrast ratio 7.2:1)

                // Focus and interaction states
                '--wsf-focus-ring' => 'rgba(37, 99, 235, 0.2)', // Blue focus ring with transparency
            ],
            'dark' => [
                'name' => __('Dark Theme', 'wheel-size'),
                // Modern dark theme with elegant, eye-friendly colors
                // Background and base colors - Deep but not pure black for reduced eye strain
                '--wsf-bg' => '#0B1120',              // Very dark blue-gray for main background
                '--wsf-surface' => '#1A1F2E',         // Slightly lighter for cards/surfaces
                '--wsf-surface-hover' => '#252A3A',   // Medium dark for hover states

                // Text colors optimized for dark backgrounds (WCAG AA compliant)
                '--wsf-text' => '#F8FAFC',            // Near-white for primary text (contrast ratio 15.8:1)
                '--wsf-text-primary' => '#F8FAFC',    // Same as main text
                '--wsf-text-secondary' => '#CBD5E1',  // Light gray for secondary text (contrast ratio 9.2:1)
                '--wsf-text-muted' => '#94A3B8',      // Medium gray for muted text (contrast ratio 5.1:1)

                // Primary brand colors - Brighter blues that work well on dark backgrounds
                '--wsf-primary' => '#3B82F6',         // Bright blue (contrast ratio 8.2:1 on dark bg)
                '--wsf-hover' => '#60A5FA',           // Lighter blue for hover states
                '--wsf-accent' => '#E5E7EB',          // Light gray accent for garage button (gray-200)
                '--wsf-on-primary' => '#FFFFFF',      // White text on primary buttons

                // Border and divider colors
                '--wsf-border' => '#374151',          // Medium gray borders
                '--wsf-secondary' => '#4B5563',       // Lighter gray for secondary elements
                '--wsf-muted' => '#1F2937',           // Dark gray for muted backgrounds

                // Input field colors
                '--wsf-input-bg' => '#1A1F2E',        // Dark input backgrounds
                '--wsf-input-text' => '#F8FAFC',      // Light text in inputs
                '--wsf-input-border' => '#374151',    // Gray input borders
                '--wsf-input-placeholder' => '#9CA3AF', // Gray placeholder text
                '--wsf-input-focus' => '#3B82F6',     // Blue focus color

                // Status colors optimized for dark theme
                '--wsf-success' => '#10B981',         // Bright green for success (contrast ratio 7.8:1)
                '--wsf-warning' => '#F59E0B',         // Bright amber for warnings (contrast ratio 8.1:1)
                '--wsf-error' => '#EF4444',           // Bright red for errors (contrast ratio 8.9:1)

                // Focus and interaction states
                '--wsf-focus-ring' => 'rgba(59, 130, 246, 0.4)', // Blue focus ring with higher opacity for dark theme
            ]
        ];
    }

    /**
     * Get theme CSS variables as string
     * 
     * @param string $slug Theme slug
     * @return string CSS variables
     */
    public static function get_theme_css_variables(string $slug): string
    {
        $theme = self::get_theme($slug);
        if (!$theme) {
            return '';
        }
        
        $css_vars = [];
        foreach ($theme as $property => $value) {
            if ($property === 'name') {
                continue;
            }
            $css_vars[] = "{$property}: {$value}";
        }
        
        return implode('; ', $css_vars);
    }
} 